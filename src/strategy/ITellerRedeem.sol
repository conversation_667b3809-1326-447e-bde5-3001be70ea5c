// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

/**
 * @notice Interface for TellerMint EDU protocol redemption functions
 */
interface ITellerRedeem {
    function redeem(uint256 shares, address receiver, address owner) external returns (bytes32 requestId);
    function claim(bytes32 requestId) external returns (uint256 assets);
    function previewRedeem(uint256 shares) external view returns (uint256 assets);
    function cooldownPeriod() external view returns (uint256 cooldownPeriod);
}
