// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

import "./token/RPT.sol";
import "./token/StakedRPT.sol";
import "./strategy/ITellerMint.sol";
import "./strategy/ITellerRedeem.sol";

contract StakePool is
    Initializable,
    AccessControlUpgradeable,
    PausableUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable
{
    using SafeERC20 for IERC20;

    // Roles
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");
    bytes32 public constant UPGRADER_ROLE = keccak256("UPGRADER_ROLE");

    RPT public rpt;
    StakedRPT public stRPT;

    event RPTStaked(address indexed user, uint256 rpt, uint256 months); // RPT → NFT
    event RPTUnstaked(address indexed user, uint256 amount, uint256 rpt); // stRPT → RPT

    // Errors
    error InvalidAmount();
    error InvalidLockDuration();
    error InsufficientLiquidity();
    error PositionNotMatured();
    error Unauthorized();
    error RequestAlreadyClaimed();
    error WaitPeriodNotCompleted();
    error InvalidRequestId();
    error EpochTooEarly();
    error StrategyNotSet();
    error StrategyDepositFailed();
    error StrategyRedeemFailed();
    error InsufficientStrategyLiquidity();
    error StrategyRequestNotReady();

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /**
     * @notice Initialize the retail pool
     * @param _rpt RPT token address
     * @param _stRPT address _stRPT,
     * @param _admin Admin address
     */
    function initialize(
        address _rpt,
        address _stRPT,
        address _admin
    ) public initializer {
        __AccessControl_init();
        __Pausable_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        rpt = RPT(_rpt);
        stRPT = StakedRPT(_stRPT);

        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(PAUSER_ROLE, _admin);
        _grantRole(UPGRADER_ROLE, _admin);
    }

    /**
     * @notice Stake RPT to earn rewards with lock-up period
     * @param rptAmount Amount of RPT to stake (18 decimals)
     * @param months Lock duration (3, 6, or 12 months)
     */
    function stakeRPT(
        uint256 rptAmount,
        uint256 months
    ) external nonReentrant whenNotPaused {
        if (rptAmount == 0) revert InvalidAmount();
        if (months != 3 && months != 6 && months != 12) {
            revert InvalidLockDuration();
        }

        // Burn RPT directly from user
        rpt.burn(msg.sender, rptAmount);

        // Mint stRPT ERC20 tokens with lock position
        stRPT.mint(msg.sender, rptAmount, months);

        emit RPTStaked(msg.sender, rptAmount, months);
    }

    /**
     * @notice Unstake matured staked positions
     * @param amount Amount of stRPT to unstake (18 decimals)
     */
    function unstakeRPT(uint256 amount) external nonReentrant whenNotPaused {
        if (amount == 0) revert InvalidAmount();
        if (!stRPT.canUnstake(msg.sender, amount)) revert PositionNotMatured();

        // Burn stRPT tokens
        stRPT.burn(msg.sender, amount);

        // Mint RPT tokens back to user
        rpt.mint(msg.sender, amount);

        emit RPTUnstaked(msg.sender, amount, amount);
    }

    /**
     * @notice Pause the contract
     */
    function pause() external onlyRole(PAUSER_ROLE) {
        _pause();
    }

    /**
     * @notice Unpause the contract
     */
    function unpause() external onlyRole(PAUSER_ROLE) {
        _unpause();
    }

    /**
     * @notice Set stRPT contract address (admin only)
     * @param _stRPT New stRPT contract address
     */
    function setStRPT(address _stRPT) external onlyRole(DEFAULT_ADMIN_ROLE) {
        stRPT = StakedRPT(_stRPT);
    }

    /**
     * @dev Authorize upgrade (only upgrader role)
     */
    function _authorizeUpgrade(
        address newImplementation
    ) internal override onlyRole(UPGRADER_ROLE) {}
}
