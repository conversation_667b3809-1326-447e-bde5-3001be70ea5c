// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title StakedRPT ERC20 Version
 * @dev ERC20 implementation of staked RPT tokens with time-based unlock and tradeable locks
 */
contract StakedRPT is
    Initializable,
    ERC20Upgradeable,
    AccessControlUpgradeable,
    PausableUpgradeable,
    UUPSUpgradeable
{
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");
    bytes32 public constant MINTER_ROLE = keccak256("MINTER_ROLE");
    bytes32 public constant UPGRADER_ROLE = keccak256("UPGRADER_ROLE");

    struct LockPosition {
        uint256 amount; // Amount of RPT locked
        uint256 lockDuration; // Lock duration in months
        uint256 lockTimestamp; // When the lock was created
        uint256 unlockTime; // When the lock expires
    }

    // User address => array of lock positions
    mapping(address => LockPosition[]) public userLocks;

    // User address => total locked amount (cannot be unstaked directly)
    mapping(address => uint256) public lockedBalances;

    // Events
    event Staked(
        address indexed user,
        uint256 amount,
        uint256 lockDuration,
        uint256 unlockTime
    );
    event Unstaked(address indexed user, uint256 amount);
    event LockTransferred(
        address indexed from,
        address indexed to,
        uint256 amount,
        uint256 unlockTime
    );

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(
        string memory name,
        string memory symbol,
        address admin,
        address _minter
    ) external initializer {
        __ERC20_init(name, symbol);
        __AccessControl_init();
        __Pausable_init();
        __UUPSUpgradeable_init();

        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(PAUSER_ROLE, admin);
        _grantRole(MINTER_ROLE, _minter);
        _grantRole(UPGRADER_ROLE, admin);
    }

    /**
     * @notice Stake RPT tokens with lock duration
     * @param user User address
     * @param amount Amount to stake
     * @param lockDuration Lock duration in months (3, 6, 12)
     */
    function mint(
        address user,
        uint256 amount,
        uint256 lockDuration
    ) external onlyRole(MINTER_ROLE) whenNotPaused {
        require(
            lockDuration == 3 || lockDuration == 6 || lockDuration == 12,
            "Invalid lock duration"
        );

        uint256 unlockTime = block.timestamp + (lockDuration * 30 days);

        // Create lock position
        userLocks[user].push(
            LockPosition({
                amount: amount,
                lockDuration: lockDuration,
                lockTimestamp: block.timestamp,
                unlockTime: unlockTime
            })
        );

        // Update locked balance
        lockedBalances[user] += amount;

        // Mint stRPT tokens
        _mint(user, amount);

        emit Staked(user, amount, lockDuration, unlockTime);
    }

    /**
     * @notice Unstake unlocked RPT tokens
     * @param user User address
     * @param amount Amount to unstake
     */
    function burn(
        address user,
        uint256 amount
    ) external onlyRole(MINTER_ROLE) whenNotPaused {
        require(balanceOf(user) >= amount, "Insufficient balance");

        uint256 unlockableAmount = getUnlockableAmount(user);
        require(unlockableAmount >= amount, "Insufficient unlocked amount");

        // Update lock positions and locked balance
        _updateLockPositions(user, amount);

        // Burn stRPT tokens
        _burn(user, amount);

        emit Unstaked(user, amount);
    }

    /**
     * @notice Get unlockable amount for a user
     * @param user User address
     * @return unlockable Amount that can be unstaked
     */
    function getUnlockableAmount(
        address user
    ) public view returns (uint256 unlockable) {
        LockPosition[] storage positions = userLocks[user];

        for (uint256 i = 0; i < positions.length; i++) {
            if (block.timestamp >= positions[i].unlockTime) {
                unlockable += positions[i].amount;
            }
        }

        return unlockable;
    }

    /**
     * @notice Get locked amount for a user
     * @param user User address
     * @return locked Amount still locked
     */
    function getLockedAmount(
        address user
    ) public view returns (uint256 locked) {
        return lockedBalances[user] - getUnlockableAmount(user);
    }

    /**
     * @notice Get user's lock positions
     * @param user User address
     * @return positions Array of lock positions
     */
    function getUserLocks(
        address user
    ) external view returns (LockPosition[] memory positions) {
        return userLocks[user];
    }

    /**
     * @notice Check if user can unstake a specific amount
     * @param user User address
     * @param amount Amount to check
     * @return canUnstake Whether the amount can be unstaked
     */
    function canUnstake(
        address user,
        uint256 amount
    ) external view returns (bool) {
        return getUnlockableAmount(user) >= amount;
    }

    /**
     * @notice Check if a position is matured (for compatibility)
     * @param user User address
     * @return isMatured Whether user has any unlockable positions
     */
    function isMatured(address user) external view returns (bool) {
        return getUnlockableAmount(user) > 0;
    }

    /**
     * @notice Get user positions (for compatibility with old interface)
     * @param user User address
     * @return tokenIds Array of position indices (for compatibility)
     */
    function positionsOf(
        address user
    ) external view returns (uint256[] memory tokenIds) {
        LockPosition[] storage positions = userLocks[user];
        uint256 activePositions = 0;

        // Count non-zero positions
        for (uint256 i = 0; i < positions.length; i++) {
            if (positions[i].amount > 0) {
                activePositions++;
            }
        }

        tokenIds = new uint256[](activePositions);
        uint256 index = 0;

        for (uint256 i = 0; i < positions.length; i++) {
            if (positions[i].amount > 0) {
                tokenIds[index] = i;
                index++;
            }
        }

        return tokenIds;
    }

    /**
     * @notice Override transfer to handle lock inheritance
     * @dev When stRPT tokens are transferred, the locked status needs special handling
     */
    function _update(
        address from,
        address to,
        uint256 value
    ) internal override whenNotPaused {
        if (from != address(0) && to != address(0)) {
            // Handle lock transfer logic
            _handleLockTransfer(from, to, value);
        }

        super._update(from, to, value);
    }

    /**
     * @notice Handle lock transfer between users
     * @dev Transfers the earliest maturing locks to maintain FIFO order
     */
    function _handleLockTransfer(
        address from,
        address to,
        uint256 amount
    ) internal {
        if (amount == 0) return;

        // Sort and transfer locks starting with earliest unlocks
        uint256 remaining = amount;
        LockPosition[] storage fromLocks = userLocks[from];

        // Find locks to transfer (FIFO - earliest first)
        for (uint256 i = 0; i < fromLocks.length && remaining > 0; i++) {
            if (fromLocks[i].amount == 0) continue;

            uint256 transferAmount = remaining > fromLocks[i].amount
                ? fromLocks[i].amount
                : remaining;

            // Create new lock for recipient
            userLocks[to].push(
                LockPosition({
                    amount: transferAmount,
                    lockDuration: fromLocks[i].lockDuration,
                    lockTimestamp: fromLocks[i].lockTimestamp,
                    unlockTime: fromLocks[i].unlockTime
                })
            );

            // Reduce sender's lock
            fromLocks[i].amount -= transferAmount;
            remaining -= transferAmount;

            // Update locked balances
            lockedBalances[from] -= transferAmount;
            lockedBalances[to] += transferAmount;

            emit LockTransferred(
                from,
                to,
                transferAmount,
                fromLocks[i].unlockTime
            );
        }
    }

    /**
     * @notice Update lock positions after unstaking
     */
    function _updateLockPositions(address user, uint256 amount) internal {
        uint256 remaining = amount;
        LockPosition[] storage positions = userLocks[user];

        // Remove unlocked positions first (FIFO)
        for (uint256 i = 0; i < positions.length && remaining > 0; i++) {
            if (
                positions[i].amount == 0 ||
                block.timestamp < positions[i].unlockTime
            ) {
                continue;
            }

            uint256 removeAmount = remaining > positions[i].amount
                ? positions[i].amount
                : remaining;
            positions[i].amount -= removeAmount;
            lockedBalances[user] -= removeAmount;
            remaining -= removeAmount;
        }
    }

    // Admin functions
    function pause() public onlyRole(PAUSER_ROLE) {
        _pause();
    }

    function unpause() public onlyRole(PAUSER_ROLE) {
        _unpause();
    }

    function _authorizeUpgrade(
        address newImplementation
    ) internal override onlyRole(UPGRADER_ROLE) {}
}
