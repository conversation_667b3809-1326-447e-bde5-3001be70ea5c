// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

import "./token/RPT.sol";
import "./token/StakedRPT.sol";
import "./strategy/ITellerMint.sol";
import "./strategy/ITellerRedeem.sol";

/**
 * @title Retail Pool
 * @notice Main contract for the retail liquidity pool
 * @dev UUPS upgradeable contract with comprehensive access control
 */
contract RetailPool is
    Initializable,
    AccessControlUpgradeable,
    PausableUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable
{
    using SafeERC20 for IERC20;

    // Roles
    bytes32 public constant FUND_MANAGER_ROLE = keccak256("FUND_MANAGER_ROLE");
    bytes32 public constant STRATEGY_ROLE = keccak256("STRATEGY_ROLE");
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");
    bytes32 public constant UPGRADER_ROLE = keccak256("UPGRADER_ROLE");

    // Constants
    uint256 public constant USDT_DECIMALS = 6;
    uint256 public constant RPT_DECIMALS = 18;
    uint256 public constant DECIMALS_DIFF = RPT_DECIMALS - USDT_DECIMALS;
    uint256 public constant PRICE_PRECISION = 1e18;

    // Contracts
    IERC20 public usdt;
    RPT public rpt;
    ITellerMint public mintStrategy;
    ITellerRedeem public redeemStrategy;

    // Pool state
    uint256 public totalWaitDeposits; // Total USDT waiting to deposit (6 decimals) - used in BATCH_MODE
    uint256 public userWaitWithdrawals; // Total USDT waiting to withdraw (6 decimals) - used in BATCH_MODE
    uint256 public adminWaitWithdrawals; // Total USDT waiting for admin to claim (6 decimals) - used in BATCH_MODE

    // Withdrawal request configuration
    uint256 public withdrawalWaitPeriod; // Configurable wait period
    uint256 private _nextWithdrawRequestId; // Unified request ID counter

    // Epoch management for strategy rebalancing
    uint256 public epochDuration; // Configurable epoch duration
    uint256 public lastEpochTime; // Last time epoch was triggered
    uint256 private _nextStrategyRequestId;
    uint256 public strategyShares; // Total shares held in strategy

    // Request type enumeration
    enum RequestType {
        USER_WITHDRAW,
        ADMIN_WITHDRAW
    }

    // Operation mode enumeration (only BATCH_MODE supported)
    enum OperationMode {
        BATCH_MODE // Wait for epoch trigger for batch processing
    }

    // Strategy request tracking
    struct StrategyRequest {
        bytes32 requestId; // Teller request ID (bytes32)
        uint256 amount; // Amount requested for redemption
        uint256 requestTime;
        bool claimed;
    }

    // Mapping from our internal ID to strategy request
    mapping(uint256 => StrategyRequest) public strategyRequests;
    uint256[] public pendingStrategyRequests;

    // Unified withdrawal request structure
    struct WithdrawRequest {
        address requester; // User or admin address
        uint256 rptAmount; // RPT amount to burn (18 decimals) - 0 for admin requests
        uint256 usdtAmount; // USDT amount to receive (6 decimals)
        uint256 requestTime;
        bool claimed;
        uint256 strategyRequestId; // ID of the associated strategy redeem request
        RequestType requestType; // USER_WITHDRAW or ADMIN_WITHDRAW
    }

    // Mapping from request ID to withdrawal request
    mapping(uint256 => WithdrawRequest) public withdrawRequests;

    // Mapping from admin/user to their withdrawal request IDs
    mapping(address => uint256[]) public adminWithdrawRequestIds;
    mapping(address => uint256[]) public userWithdrawRequestIds;

    // Events
    event USDTDeposited(address indexed user, uint256 usdt, uint256 rpt); // USDT → RPT
    event WithdrawRequested(
        address indexed user,
        uint256 indexed requestId,
        uint256 rpt,
        uint256 usdt
    );
    event WithdrawClaimed(
        address indexed user,
        uint256 indexed requestId,
        uint256 rpt,
        uint256 usdt
    );
    event ImmediateWithdrawCompleted(
        address indexed user,
        uint256 rpt,
        uint256 usdt
    );
    event AdminImmediateWithdrawCompleted(
        address indexed admin,
        uint256 usdt
    );
    event AdminWithdrawRequested(
        address indexed admin,
        uint256 indexed requestId,
        uint256 usdt
    );
    event AdminWithdrawClaimed(
        address indexed admin,
        uint256 indexed requestId,
        uint256 usdt
    );
    event FundsInjected(address indexed manager, uint256 usdt);
    event WithdrawalWaitPeriodUpdated(uint256 oldPeriod, uint256 newPeriod);
    event EpochTriggered(
        address indexed trigger,
        uint256 epochTime,
        int256 netAmount
    );
    event StrategyDeposited(uint256 amount, uint256 shares);
    event StrategyRedeemRequested(
        uint256 indexed internalId,
        bytes32 indexed tellerRequestId,
        uint256 amount
    );
    event StrategyRedeemClaimed(uint256 indexed internalId, uint256 amount);
    event EpochDurationUpdated(uint256 oldDuration, uint256 newDuration);

    // Errors
    error InvalidAmount();
    error InvalidLockDuration();
    error InsufficientLiquidity();
    error PositionNotMatured();
    error Unauthorized();
    error RequestAlreadyClaimed();
    error WaitPeriodNotCompleted();
    error InvalidRequestId();
    error EpochTooEarly();
    error StrategyNotSet();
    error StrategyDepositFailed();
    error StrategyRedeemFailed();
    error InsufficientStrategyLiquidity();
    error StrategyRequestNotReady();

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /**
     * @notice Initialize the retail pool
     * @param _usdt USDT token address
     * @param _rpt RPT token address
     * @param _mintStrategy EDU mint strategy address
     * @param _redeemStrategy EDU redeem strategy address
     * @param _admin Admin address
     * @param _fundManager Fund manager address
     */
    function initialize(
        address _usdt,
        address _rpt,
        address _mintStrategy,
        address _redeemStrategy,
        address _admin,
        address _fundManager
    ) public initializer {
        __AccessControl_init();
        __Pausable_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        usdt = IERC20(_usdt);
        rpt = RPT(_rpt);
        mintStrategy = ITellerMint(_mintStrategy);
        redeemStrategy = ITellerRedeem(_redeemStrategy);

        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(FUND_MANAGER_ROLE, _fundManager);
        _grantRole(STRATEGY_ROLE, _mintStrategy);
        _grantRole(STRATEGY_ROLE, _redeemStrategy);
        _grantRole(PAUSER_ROLE, _admin);
        _grantRole(UPGRADER_ROLE, _admin);

        // Initialize request counters
        _nextWithdrawRequestId = 1;
        _nextStrategyRequestId = 1;

        // Initialize configuration
        withdrawalWaitPeriod = 7 days;
        epochDuration = 24 hours;
    }

    /**
     * @notice Deposit USDT and receive RPT
     * @param amount Amount of USDT to deposit (6 decimals)
     * @dev Funds wait for epoch trigger for batch processing
     */
    function deposit(uint256 amount) external nonReentrant whenNotPaused {
        if (amount == 0) revert InvalidAmount();

        // Calculate RPT to mint based on current price BEFORE transferring USDT
        uint256 rptToMint = _calculateRPTAmount(amount);

        // Transfer USDT from user
        usdt.safeTransferFrom(msg.sender, address(this), amount);

        // Add to wait deposits and immediate pool
        totalWaitDeposits += amount;

        // Regular position - mint full RPT
        rpt.mint(msg.sender, rptToMint);

        emit USDTDeposited(msg.sender, amount, rptToMint);
    }

    /**
     * @notice Attempt immediate withdrawal using available immediate pool funds
     * @param rptAmount Amount of RPT to burn (18 decimals)
     * @return success Whether the immediate withdrawal was successful
     * @dev Uses immediate pool funds when available
     */
    function withdrawImmediately(
        uint256 rptAmount
    ) external nonReentrant whenNotPaused returns (bool success) {
        if (rptAmount == 0) revert InvalidAmount();

        // Calculate USDT to return based on current price
        uint256 usdtAmount = _calculateUSDTAmount(rptAmount);

        // Check if immediate pool has sufficient funds
        if (usdtAmount > totalWaitDeposits) {
            return false;
        }

        // Perform immediate withdrawal atomically
        rpt.burn(msg.sender, rptAmount);
        totalWaitDeposits -= usdtAmount;
        usdt.safeTransfer(msg.sender, usdtAmount);

        emit ImmediateWithdrawCompleted(msg.sender, rptAmount, usdtAmount);
        return true;
    }

    /**
     * @notice Check if a withdrawal amount can be completed immediately
     * @param rptAmount Amount of RPT to withdraw (18 decimals)
     * @return canWithdraw Whether the amount can be withdrawn immediately
     */
    function canWithdrawImmediately(
        uint256 rptAmount
    ) external view returns (bool canWithdraw) {
        if (rptAmount == 0) {
            return false;
        }

        uint256 usdtAmount = _calculateUSDTAmount(rptAmount);
        return usdtAmount <= totalWaitDeposits;
    }

    /**
     * @notice Attempt immediate admin withdrawal using available immediate pool funds
     * @param usdtAmount Amount of USDT to withdraw (6 decimals)
     * @return success Whether the immediate withdrawal was successful
     * @dev Only works when immediate pool has sufficient funds
     */
    function adminWithdrawImmediately(
        uint256 usdtAmount
    ) external onlyRole(FUND_MANAGER_ROLE) nonReentrant whenNotPaused returns (bool success) {
        if (usdtAmount == 0) revert InvalidAmount();

        // Check if immediate pool has sufficient funds
        if (usdtAmount > totalWaitDeposits) {
            return false;
        }

        // Check admin liquidity limits
        uint256 availableLiquidity = _getAdminAvailableLiquidity();
        if (usdtAmount > availableLiquidity) {
            return false;
        }

        // Perform immediate withdrawal atomically
        totalWaitDeposits -= usdtAmount;
        usdt.safeTransfer(msg.sender, usdtAmount);

        emit AdminImmediateWithdrawCompleted(msg.sender, usdtAmount);
        return true;
    }

    /**
     * @notice Check if an admin withdrawal amount can be completed immediately
     * @param usdtAmount Amount of USDT to withdraw (6 decimals)
     * @return canWithdraw Whether the amount can be withdrawn immediately
     */
    function canAdminWithdrawImmediately(
        uint256 usdtAmount
    ) external view returns (bool canWithdraw) {
        if (usdtAmount == 0) {
            return false;
        }

        // Check immediate pool availability
        if (usdtAmount > totalWaitDeposits) {
            return false;
        }

        // Check admin liquidity limits
        uint256 availableLiquidity = _getAdminAvailableLiquidity();
        return usdtAmount <= availableLiquidity;
    }

    /**
     * @notice Request withdrawal by burning RPT
     * @param rptAmount Amount of RPT to burn (18 decimals)
     * @return requestId The ID of the withdrawal request
     * @dev Funds wait for epoch trigger for batch processing
     */
    function requestWithdraw(
        uint256 rptAmount
    ) external nonReentrant whenNotPaused returns (uint256 requestId) {
        if (rptAmount == 0) revert InvalidAmount();

        // Calculate USDT to return based on current price
        uint256 usdtAmount = _calculateUSDTAmount(rptAmount);

        // Check if there's enough liquidity for this withdrawal request
        uint256 availableLiquidity = liquidity();
        if (usdtAmount > availableLiquidity) revert InsufficientLiquidity();

        // Burn RPT from user immediately
        rpt.burn(msg.sender, rptAmount);

        // Create withdrawal request
        requestId = _nextWithdrawRequestId++;

        // Use unified processing function
        _processWithdrawRequest(requestId, rptAmount, usdtAmount, RequestType.USER_WITHDRAW);

        // Add to user's request list
        userWithdrawRequestIds[msg.sender].push(requestId);

        emit WithdrawRequested(msg.sender, requestId, rptAmount, usdtAmount);
    }

    /**
     * @notice Set withdrawal wait period (admin only)
     * @param newPeriod New wait period in seconds
     */
    function setWithdrawalWaitPeriod(
        uint256 newPeriod
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        uint256 oldPeriod = withdrawalWaitPeriod;
        withdrawalWaitPeriod = newPeriod;
        emit WithdrawalWaitPeriodUpdated(oldPeriod, newPeriod);
    }

    /**
     * @notice Get user's withdrawal requests
     * @param user User address
     * @return Array of withdrawal request IDs for the user
     */
    function getUserWithdrawRequests(
        address user
    ) external view returns (uint256[] memory) {
        return userWithdrawRequestIds[user];
    }

    /**
     * @notice Check if withdrawal request can be claimed (unified for both user and admin requests)
     * @param requestId The withdrawal request ID
     * @return canClaim Whether the request can be claimed
     * @return timeRemaining Seconds remaining until claimable (0 if already claimable)
     */
    function canClaimWithdraw(
        uint256 requestId
    ) external view returns (bool canClaim, uint256 timeRemaining) {
        WithdrawRequest storage request = withdrawRequests[requestId];

        // Check if request exists and is not claimed
        if (request.requester == address(0) || request.claimed) {
            return (false, 0);
        }

        uint256 claimTime = request.requestTime + withdrawalWaitPeriod;
        if (block.timestamp >= claimTime) {
            return (true, 0);
        } else {
            return (false, claimTime - block.timestamp);
        }
    }

    /**
     * @notice Fund manager requests withdrawal
     * @param usdtAmount Amount of USDT to withdraw (6 decimals)
     * @return requestId The ID of the admin withdrawal request
     * @dev Funds wait for epoch trigger for batch processing
     */
    function adminRequestWithdraw(
        uint256 usdtAmount
    )
        external
        onlyRole(FUND_MANAGER_ROLE)
        nonReentrant
        whenNotPaused
        returns (uint256 requestId)
    {
        if (usdtAmount == 0) revert InvalidAmount();

        // Check that requested amount doesn't exceed available liquidity
        uint256 availableLiquidity = _getAdminAvailableLiquidity();
        if (usdtAmount > availableLiquidity) revert InsufficientLiquidity();

        // Create admin withdrawal request
        requestId = _nextWithdrawRequestId++;

        // Use unified processing function
        _processWithdrawRequest(requestId, 0, usdtAmount, RequestType.ADMIN_WITHDRAW);

        // Add to admin's request list
        adminWithdrawRequestIds[msg.sender].push(requestId);

        emit AdminWithdrawRequested(msg.sender, requestId, usdtAmount);
    }

    /**
     * @notice Unified claim function for both user and admin withdrawals
     * @param requestId The withdrawal request ID to claim
     * @dev Automatically determines request type and applies appropriate permissions
     */
    function claimWithdraw(
        uint256 requestId
    ) external nonReentrant whenNotPaused {
        WithdrawRequest storage request = withdrawRequests[requestId];

        if (request.requester == address(0)) revert InvalidRequestId();
        if (request.requester != msg.sender) revert Unauthorized();
        if (request.claimed) revert RequestAlreadyClaimed();
        if (block.timestamp < request.requestTime + withdrawalWaitPeriod) {
            revert WaitPeriodNotCompleted();
        }

        // Check role-based permissions
        if (request.requestType == RequestType.ADMIN_WITHDRAW) {
            if (!hasRole(FUND_MANAGER_ROLE, msg.sender)) revert Unauthorized();
        }

        // Handle strategy redeem and transfer
        _processClaimWithdraw(
            request.strategyRequestId,
            request.usdtAmount,
            msg.sender
        );

        // Mark as claimed
        request.claimed = true;

        // Emit appropriate event based on request type
        if (request.requestType == RequestType.ADMIN_WITHDRAW) {
            emit AdminWithdrawClaimed(
                msg.sender,
                requestId,
                request.usdtAmount
            );
        } else {
            emit WithdrawClaimed(
                msg.sender,
                requestId,
                request.rptAmount,
                request.usdtAmount
            );
        }
    }

    /**
     * @notice Get admin's withdrawal requests
     * @param admin Admin address
     * @return Array of admin withdrawal request IDs
     */
    function getAdminWithdrawRequests(
        address admin
    ) external view returns (uint256[] memory) {
        return adminWithdrawRequestIds[admin];
    }

    /**
     * @notice Inject additional funds into the pool (fund manager only)
     * @param usdtAmount Amount of USDT to inject (6 decimals)
     * @dev Funds wait for epoch trigger for batch processing
     */
    function injectFunds(
        uint256 usdtAmount
    ) external onlyRole(FUND_MANAGER_ROLE) nonReentrant whenNotPaused {
        if (usdtAmount == 0) revert InvalidAmount();

        // Transfer USDT from fund manager to contract
        usdt.safeTransferFrom(msg.sender, address(this), usdtAmount);

        totalWaitDeposits += usdtAmount;

        emit FundsInjected(msg.sender, usdtAmount);
    }



    /**
     * @notice Get available liquidity for users (total - all reserved funds)
     * @return Available USDT liquidity for user operations
     * @dev Uses batch mode calculation with wait withdrawals
     */
    function liquidity() public view returns (uint256) {
        // Calculate total available liquidity including strategy funds
        uint256 totalBalance = usdt.balanceOf(address(this));

        // Add strategy value that can be redeemed
        if (address(redeemStrategy) != address(0) && strategyShares > 0) {
            try redeemStrategy.previewRedeem(strategyShares) returns (
                uint256 strategyValue
            ) {
                totalBalance += strategyValue;
            } catch {
                // If preview fails, don't count strategy value
            }
        }

        // In batch mode, use traditional calculation with wait withdrawals
        uint256 adminReserved = adminWaitWithdrawals;
        uint256 userReserved = userWaitWithdrawals;
        uint256 reservedAmount = adminReserved + userReserved;

        if (totalBalance > reservedAmount) {
            return totalBalance - reservedAmount;
        } else {
            return 0;
        }
    }



    /**
     * @notice Calculate liquidity available for admin withdrawal
     * @dev Uses batch mode calculation
     */
    function _getAdminAvailableLiquidity() internal view returns (uint256) {
        // In batch mode, use traditional calculation
        uint256 totalBalance = usdt.balanceOf(address(this));
        uint256 userReserved = userWaitWithdrawals;
        uint256 adminReserved = adminWaitWithdrawals;

        uint256 totalReserved = userReserved + adminReserved;
        if (totalBalance > totalReserved) {
            return totalBalance - totalReserved;
        } else {
            return 0;
        }
    }

    /**
     * @notice Get current RPT price based on actual USDT backing
     * @return RPT price in USDT (18 decimals)
     */
    function rptPrice() public view returns (uint256) {
        uint256 totalSupply = rpt.totalSupply();
        if (totalSupply == 0) {
            return PRICE_PRECISION; // 1:1 for initial price
        }

        // Calculate total USDT value backing RPT tokens
        uint256 totalUSDTValue = _getTotalUSDTBacking();

        // Price = Total USDT Value / Total RPT Supply
        // Handle decimal differences: USDT is 6 decimals, RPT is 18, price should be 18
        return
            (totalUSDTValue * PRICE_PRECISION * (10 ** DECIMALS_DIFF)) /
            totalSupply;
    }

    /**
     * @notice Get total USDT value backing all RPT tokens
     * @return Total USDT value (6 decimals)
     */
    function _getTotalUSDTBacking() internal view returns (uint256) {
        // Current USDT balance in the contract
        uint256 currentBalance = usdt.balanceOf(address(this));

        // USDT equivalent of strategy shares (if strategy is set)
        uint256 strategyValue = 0;
        if (address(redeemStrategy) != address(0) && strategyShares > 0) {
            try redeemStrategy.previewRedeem(strategyShares) returns (
                uint256 redeemableAmount
            ) {
                strategyValue = redeemableAmount;
            } catch {
                // If preview fails, assume strategy value is 0
                strategyValue = 0;
            }
        }

        // Total backing is contract balance plus strategy value
        // This represents the total value backing all RPT tokens
        return currentBalance + strategyValue;
    }

    /**
     * @notice Pause the contract
     */
    function pause() external onlyRole(PAUSER_ROLE) {
        _pause();
    }

    /**
     * @notice Unpause the contract
     */
    function unpause() external onlyRole(PAUSER_ROLE) {
        _unpause();
    }

    /**
     * @notice Calculate RPT amount from USDT
     * @param usdtAmount USDT amount (6 decimals)
     * @return RPT amount (18 decimals)
     */
    function _calculateRPTAmount(
        uint256 usdtAmount
    ) private view returns (uint256) {
        uint256 currentPrice = rptPrice();
        // RPT = USDT * 10^18 * 10^12 / price
        return
            (usdtAmount * PRICE_PRECISION * (10 ** DECIMALS_DIFF)) /
            currentPrice;
    }

    /**
     * @notice Calculate USDT amount from RPT
     * @param rptAmount RPT amount (18 decimals)
     * @return USDT amount (6 decimals)
     */
    function _calculateUSDTAmount(
        uint256 rptAmount
    ) private view returns (uint256) {
        uint256 currentPrice = rptPrice();
        // USDT = RPT * price / 10^18 / 10^12
        return
            (rptAmount * currentPrice) /
            PRICE_PRECISION /
            (10 ** DECIMALS_DIFF);
    }

    /**
     * @notice Calculate shares needed to redeem a specific USDT amount
     * @param usdtAmount USDT amount to redeem (6 decimals)
     * @return shares Number of shares needed
     */
    function _calculateSharesToRedeem(
        uint256 usdtAmount
    ) internal view returns (uint256) {
        if (strategyShares == 0) return 0;

        try redeemStrategy.previewRedeem(strategyShares) returns (
            uint256 totalRedeemable
        ) {
            if (totalRedeemable == 0) return 0;

            // Calculate proportional shares needed
            uint256 sharesToRedeem = (usdtAmount * strategyShares) /
                totalRedeemable;
            // Don't redeem more shares than we have
            return
                sharesToRedeem > strategyShares
                    ? strategyShares
                    : sharesToRedeem;
        } catch {
            // If preview fails, use simple proportion
            return usdtAmount > strategyShares ? strategyShares : usdtAmount;
        }
    }

    /**
     * @notice Remove a strategy request from pending requests array
     * @param strategyRequestId The strategy request ID to remove
     */
    function _removeFromPendingRequests(uint256 strategyRequestId) internal {
        uint256 length = pendingStrategyRequests.length;
        for (uint256 i = 0; i < length; i++) {
            if (pendingStrategyRequests[i] == strategyRequestId) {
                // Replace with last element and pop
                pendingStrategyRequests[i] = pendingStrategyRequests[
                    length - 1
                ];
                pendingStrategyRequests.pop();
                break;
            }
        }
    }

    /**
     * @notice Process claim withdrawal including strategy redeem and USDT transfer
     * @param strategyRequestId The strategy request ID (0 if batch mode)
     * @param usdtAmount The USDT amount to transfer
     * @param recipient The recipient address
     */
    function _processClaimWithdraw(
        uint256 strategyRequestId,
        uint256 usdtAmount,
        address recipient
    ) internal {
        // Check if associated strategy redeem is complete (only for instant mode)
        if (strategyRequestId != 0) {
            StrategyRequest storage strategyRequest = strategyRequests[
                strategyRequestId
            ];
            if (!strategyRequest.claimed) {
                // Try to claim the strategy redeem if cooldown period has passed
                uint256 cooldown = redeemStrategy.cooldownPeriod();
                if (block.timestamp >= strategyRequest.requestTime + cooldown) {
                    try
                        redeemStrategy.claim(strategyRequest.requestId)
                    returns (uint256 claimedAmount) {
                        strategyRequest.claimed = true;
                        _removeFromPendingRequests(strategyRequestId);
                        emit StrategyRedeemClaimed(
                            strategyRequestId,
                            claimedAmount
                        );
                    } catch {
                        revert StrategyRequestNotReady();
                    }
                } else {
                    revert StrategyRequestNotReady();
                }
            }
        }

        // Check liquidity at claim time
        if (usdtAmount > usdt.balanceOf(address(this))) {
            revert InsufficientLiquidity();
        }

        // Transfer USDT to recipient
        usdt.safeTransfer(recipient, usdtAmount);
    }



    /**
     * @notice Trigger epoch processing for batch operations
     * @dev Process batch operations and claim redemptions
     */
    function triggerEpoch() external {
        if (block.timestamp < lastEpochTime + epochDuration) {
            revert EpochTooEarly();
        }

        // Update last epoch time
        lastEpochTime = block.timestamp;

        // Process batch operations
        _triggerEpochBatchMode();
    }

    /**
     * @notice Claim pending strategy redemption requests that have completed cooldown
     */
    function _claimPendingRedemptions() internal {
        if (address(redeemStrategy) == address(0)) return;

        uint256 cooldown = redeemStrategy.cooldownPeriod();
        uint256 length = pendingStrategyRequests.length;

        for (uint256 i = 0; i < length; ) {
            uint256 internalId = pendingStrategyRequests[i];
            StrategyRequest storage request = strategyRequests[internalId];

            // Check if cooldown period has passed and not yet claimed
            if (
                !request.claimed &&
                block.timestamp >= request.requestTime + cooldown
            ) {
                try redeemStrategy.claim(request.requestId) returns (
                    uint256 claimedAmount
                ) {
                    request.claimed = true;

                    // Remove from pending array by swapping with last element
                    pendingStrategyRequests[i] = pendingStrategyRequests[
                        length - 1
                    ];
                    pendingStrategyRequests.pop();
                    length--;

                    emit StrategyRedeemClaimed(internalId, claimedAmount);
                    continue; // Don't increment i since we swapped elements
                } catch {
                    // todo: Claim failed, maybe not ready yet, continue to next
                }
            }

            unchecked {
                ++i;
            }
        }
    }

    /**
     * @notice Set epoch duration (admin only)
     * @param newDuration New epoch duration in seconds
     */
    function setEpochDuration(
        uint256 newDuration
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        uint256 oldDuration = epochDuration;
        epochDuration = newDuration;
        emit EpochDurationUpdated(oldDuration, newDuration);
    }

    /**
     * @notice Manually trigger claiming of specific strategy redemption (admin)
     * @param internalId Internal ID of the strategy request to claim
     */
    function claimStrategyRedemption(
        uint256 internalId
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        StrategyRequest storage request = strategyRequests[internalId];
        if (request.claimed) revert RequestAlreadyClaimed();

        uint256 claimedAmount = redeemStrategy.claim(request.requestId);
        request.claimed = true;

        // Remove from pending array
        uint256 length = pendingStrategyRequests.length;
        for (uint256 i = 0; i < length; i++) {
            if (pendingStrategyRequests[i] == internalId) {
                pendingStrategyRequests[i] = pendingStrategyRequests[
                    length - 1
                ];
                pendingStrategyRequests.pop();
                break;
            }
        }

        emit StrategyRedeemClaimed(internalId, claimedAmount);
    }


    // ================== Internal Mode-Specific Functions ==================

    /**
     * @notice Handle withdrawal requests in batch mode
     * @param requestId The withdrawal request ID
     * @param rptAmount Amount of RPT to burn (0 for admin requests)
     * @param usdtAmount Amount of USDT to withdraw
     * @param requestType Type of request (USER_WITHDRAW or ADMIN_WITHDRAW)
     */
    function _processWithdrawRequest(
        uint256 requestId,
        uint256 rptAmount,
        uint256 usdtAmount,
        RequestType requestType
    ) internal {
        // Create withdrawal request
        withdrawRequests[requestId] = WithdrawRequest({
            requester: msg.sender,
            rptAmount: rptAmount,
            usdtAmount: usdtAmount,
            requestTime: block.timestamp,
            claimed: false,
            strategyRequestId: 0, // No strategy request in batch mode
            requestType: requestType
        });

        // Track withdrawal amounts for batch processing
        if (requestType == RequestType.USER_WITHDRAW) {
            userWaitWithdrawals += usdtAmount;
        } else {
            adminWaitWithdrawals += usdtAmount;
        }
    }





    /**
     * @notice Handle epoch trigger in batch mode
     */
    function _triggerEpochBatchMode() internal {
        if (
            address(mintStrategy) == address(0) ||
            address(redeemStrategy) == address(0)
        ) revert StrategyNotSet();

        // First, claim any pending strategy redemptions that are ready
        _claimPendingRedemptions();

        // Calculate net amount: positive means we need to deposit, negative means redeem
        // Consider user deposits, injected funds, and both user/admin pending withdrawals
        int256 netAmount = int256(totalWaitDeposits) +
            int256(userWaitWithdrawals) -
            int256(adminWaitWithdrawals);

        if (netAmount > 0) {
            // We have excess deposits to stake in strategy
            uint256 amountToDeposit = uint256(netAmount);
            uint256 currentBalance = usdt.balanceOf(address(this));

            // Only deposit what we actually have
            if (amountToDeposit > currentBalance) {
                amountToDeposit = currentBalance;
            }

            if (amountToDeposit > 0) {
                // Approve and deposit to strategy
                usdt.approve(address(mintStrategy), amountToDeposit);
                uint256 shares = mintStrategy.deposit(
                    amountToDeposit,
                    address(this)
                );

                // Update our strategy shares balance
                strategyShares += shares;

                emit StrategyDeposited(amountToDeposit, shares);
            }

            // Reset deposit and injection counters after processing
            totalWaitDeposits = 0;
        } else if (netAmount < 0) {
            // We need to redeem from strategy to cover withdrawal requests
            uint256 amountToRedeem = uint256(-netAmount);

            // Calculate shares needed to redeem the required amount
            uint256 sharesToRedeem = _calculateSharesToRedeem(amountToRedeem);

            if (sharesToRedeem > 0 && sharesToRedeem <= strategyShares) {
                bytes32 tellerRequestId = redeemStrategy.redeem(
                    sharesToRedeem,
                    address(this),
                    address(this)
                );

                // Update our strategy shares balance (reduce by redeemed shares)
                strategyShares -= sharesToRedeem;

                // Track this redemption request
                uint256 internalId = _nextStrategyRequestId++;
                strategyRequests[internalId] = StrategyRequest({
                    requestId: tellerRequestId,
                    amount: amountToRedeem,
                    requestTime: block.timestamp,
                    claimed: false
                });

                pendingStrategyRequests.push(internalId);

                emit StrategyRedeemRequested(
                    internalId,
                    tellerRequestId,
                    amountToRedeem
                );
            }

            // Reset withdrawal counters after processing redemption requests
            userWaitWithdrawals = 0;
            adminWaitWithdrawals = 0;
        }

        emit EpochTriggered(msg.sender, block.timestamp, netAmount);
    }

    /**
     * @dev Authorize upgrade (only upgrader role)
     */
    function _authorizeUpgrade(
        address newImplementation
    ) internal override onlyRole(UPGRADER_ROLE) {}
}
