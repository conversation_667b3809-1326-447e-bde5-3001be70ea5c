# Retail Pool System

The Retail Pool System implemented in this code repository is the V1.1 version of Pencil Finance, and it has no functional coupling with the V1 version.

## Overview

The Retail Pool system allows users to:

- Deposit USDT to earn yield through pooled investments
- Stake RPT tokens for fixed-term locks with enhanced yield potential
- Participate in external yield strategies through modular interfaces
- Manage withdrawals through epoch-based rebalancing
- Fund managers can inject additional liquidity and manage pool operations

## Architecture

### Core Contracts

- **RetailPool**: Main pool contract handling deposits, withdrawals, staking, and strategy integration
- **RPT**: ERC20 token representing pool shares (18 decimals)
- **StakedRPT**: ERC20 token representing locked staking positions with time-based unlocks
- **Strategy Interfaces**: Modular interfaces for yield strategy integration

### Key Features

- **UUPS Proxy Pattern**: All contracts are upgradeable using OpenZeppelin's UUPS pattern
- **Role-Based Access Control**: Comprehensive permission system for different operations
- **Modular Strategy System**: Separate interfaces for minting and redemption operations
- **Staked Positions**: 3, 6, or 12-month locks with tradeable stRPT tokens
- **Epoch-Based Rebalancing**: Automated fund management through configurable epochs
- **Fund Injection**: Admin capability to inject additional liquidity
- **Decimal Handling**: Proper conversion between USDT (6 decimals) and RPT (18 decimals)

## Contract Details

### RetailPool.sol

- Main pool logic with deposit/withdraw functionality
- Staked position management with time-based unlock system
- Epoch-based fund rebalancing with strategy integration
- Admin fund injection and withdrawal capabilities
- Price calculation based on total value including strategy positions
- Dual strategy system (separate mint and redeem strategies)

### RPT.sol (ERC20)

- Pool share token with 18 decimals
- Minter/burner roles for pool operations
- Pausable and upgradeable

### StakedRPT.sol (ERC20)

- ERC20 token representing locked staking positions
- Time-based unlock system with transferable locks
- Lock positions with duration tracking (3, 6, or 12 months)
- FIFO-based unlock and transfer mechanics

### Strategy Interfaces

- **ITellerMint**: Interface for deposit operations to yield strategies
- **ITellerRedeem**: Interface for redemption and claim operations from yield strategies
- Modular design allows independent strategy upgrades
- Support for preview operations and cooldown periods

## Staking System

The system supports time-locked staking with the following options:

| Duration  | Lock Period | Features                           |
| --------- | ----------- | ---------------------------------- |
| 3 months  | 90 days     | Fixed lock, tradeable stRPT tokens |
| 6 months  | 180 days    | Fixed lock, tradeable stRPT tokens |
| 12 months | 360 days    | Fixed lock, tradeable stRPT tokens |

### Staking Mechanics

- Users can stake RPT tokens for fixed periods
- Staked tokens are burned and stRPT tokens are minted 1:1
- stRPT tokens are transferable (locks transfer with tokens)
- Unstaking is only possible after lock expiry
- FIFO unlock system processes earliest unlocks first

## Testing

The project includes comprehensive test suites:

- **RetailPool.t.sol**: Core pool functionality tests
- **FundInjection.t.sol**: Fund injection and admin operations tests
- **EdgeCases.t.sol**: Edge cases and security tests
- **AdminWithdraw.t.sol**: Admin withdrawal functionality tests

### Running Tests

```bash
# install dependencies
forge soldeer install

# Run all tests
forge test

# Run with verbosity
forge test -vv

# Run specific test file
forge test --match-contract RetailPoolTest

# Run specific test function
forge test --match-test testStakeRPT
```

## Deployment

### Prerequisites

1. Install Foundry
2. Set environment variables for deployment

### Local Deployment

```bash
# Deploy to local network
forge script script/Deploy.s.sol --fork-url $RPC_URL --broadcast

# Setup operational contracts
forge script script/Operations.s.sol --sig "checkStatus()" --fork-url $RPC_URL
```

### Environment Variables

```bash
# Deployment addresses (optional, will deploy mocks if not provided)
USDT_ADDRESS=0x...
MINT_STRATEGY_ADDRESS=0x...
REDEEM_STRATEGY_ADDRESS=0x...

# Role addresses
ADMIN_ADDRESS=0x...
FUND_MANAGER_ADDRESS=0x...

# Operation amounts
AMOUNT=1000000000  # 1000 USDT (6 decimals)
REQUEST_ID=1       # For claim operations
```

## Usage Examples

### Basic Operations

```bash
# Check pool status
forge script script/Operations.s.sol --sig "checkStatus()" --fork-url $RPC_URL

# Inject funds (Fund Manager only)
AMOUNT=1000000000 forge script script/Operations.s.sol --sig "injectFunds()" --fork-url $RPC_URL --broadcast

# Trigger epoch rebalancing
forge script script/Operations.s.sol --sig "triggerEpoch()" --fork-url $RPC_URL --broadcast

# Check user positions
USER_ADDRESS=0x... forge script script/Operations.s.sol --sig "checkUserPositions()" --fork-url $RPC_URL
```

### Strategy Operations

```bash
# Begin strategy redemption
AMOUNT=2000000000 forge script script/Operations.s.sol --sig "beginRedemption()" --fork-url $RPC_URL --broadcast

# Claim from strategy
REQUEST_ID=1 forge script script/Operations.s.sol --sig "claimFromStrategy()" --fork-url $RPC_URL --broadcast

# Set strategies (Admin only)
MINT_STRATEGY=0x... REDEEM_STRATEGY=0x... forge script script/Operations.s.sol --sig "setStrategies()" --fork-url $RPC_URL --broadcast
```

### User Operations

```bash
# Deposit USDT
AMOUNT=1000000000 USER_ADDRESS=0x... forge script script/Operations.s.sol --sig "userDeposit()" --fork-url $RPC_URL --broadcast

# Stake RPT tokens
AMOUNT=500000000000000000000 MONTHS=6 USER_ADDRESS=0x... forge script script/Operations.s.sol --sig "stakeRPT()" --fork-url $RPC_URL --broadcast

# Request withdrawal
AMOUNT=300000000000000000000 USER_ADDRESS=0x... forge script script/Operations.s.sol --sig "requestWithdraw()" --fork-url $RPC_URL --broadcast
```

## Key Design Decisions

### Modular Strategy Architecture

- **Interface Separation**: Split strategy interface into `ITellerMint` and `ITellerRedeem`
- **Independent Upgrades**: Mint and redeem strategies can be upgraded separately
- **Flexible Integration**: Supports different protocols for deposits vs withdrawals
- **Preview Operations**: Strategy value calculation without state changes

### Staking System Redesign

- **ERC20 Staked Tokens**: Switched from ERC721 NFTs to ERC20 stRPT tokens
- **Transferable Locks**: Lock positions can be traded between users
- **FIFO Unlock System**: Earliest locks are processed first for unstaking
- **Time-based Maturity**: Fixed lock periods with automatic unlock eligibility

### Epoch-Based Fund Management

- **Automated Rebalancing**: Configurable epochs trigger strategy deposits/withdrawals
- **Net Flow Calculation**: Considers all pending deposits, withdrawals, and injections
- **Strategy Coordination**: Seamlessly moves funds between pool and strategies

### OpenZeppelin v5 Compatibility

- Migrated from v4 to v5, handling breaking changes:
  - Removed `CountersUpgradeable` - manual counter management
  - Removed `safeApprove` - using `forceApprove`
  - Updated import paths for utils
  - Fixed multiple inheritance overrides

### Decimal Precision

- USDT: 6 decimals (1 USDT = 1,000,000 units)
- RPT: 18 decimals (1 RPT = 1e18 units)
- stRPT: 18 decimals (1 stRPT = 1e18 units)
- Price calculations handle conversion properly

### Security Features

- Reentrancy protection on all external functions
- Pausable contracts for emergency stops
- Role-based access control
- Safe math operations with underflow protection
- UUPS upgrade authorization controls

## Architecture Diagrams

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  RetailPool │────│     RPT     │────│  StakedRPT  │
│   (UUPS)    │    │   (ERC20)   │    │   (ERC20)   │
└─────────────┘    └─────────────┘    └─────────────┘
       │
       ├─────────── Mint Strategy Interface
       │           ┌─────────────────┐
       └───────────│ ITellerMint     │
                   │ - deposit()     │
                   │ - previewDeposit│
                   └─────────────────┘
                           │
       ┌─────────── Redeem Strategy Interface
       │           ┌─────────────────┐
       └───────────│ ITellerRedeem   │
                   │ - redeem()      │
                   │ - claim()       │
                   │ - previewRedeem │
                   │ - cooldownPeriod│
                   └─────────────────┘

Strategy Implementations (Examples):
┌─────────────┐    ┌─────────────┐
│MockTellerMint│   │MockTellerRdm│
│             │    │             │
└─────────────┘    └─────────────┘
```

## Development Notes

### Testing Strategy

- Unit tests for individual contract functionality
- Integration tests for cross-contract interactions
- Edge case tests for boundary conditions
- Mock contracts for external dependencies

### Upgrade Safety

- All contracts use UUPS proxy pattern
- Storage layouts are carefully managed
- Upgrade authorization is role-protected
- Implementation contracts have initializer protection

### Gas Optimization

- Efficient storage packing
- Minimal external calls
- Batch operations where possible
- View functions for off-chain calculations

## Security Considerations

- All external functions have reentrancy protection
- Role-based access control prevents unauthorized operations
- Safe math operations prevent overflow/underflow
- Pausable mechanisms for emergency stops
- Upgrade controls restrict implementation changes
