[profile.default]
src = "src"
out = "out"
libs = ["dependencies"]

# Additional settings for testing and compilation
ffi = true
ast = true
build_info = true
extra_output = ["storageLayout", "metadata"]

[dependencies]
forge-std = "1.10.0"
"@openzeppelin-contracts" = "5.4.0"
"@openzeppelin-contracts-upgradeable" = "5.4.0"
openzeppelin-foundry-upgrades = "0.4.0"

[fmt]
bracket_spacing = false
int_types = "long"
line_length = 120
multiline_func_header = "params_first"
number_underscore = "thousands"
quote_style = "double"
tab_width = 4

# Enables or disables the optimizer
optimizer = true
# The number of optimizer runs
optimizer_runs = 200
# Whether to use the Yul intermediate representation compilation pipeline
via_ir = true
# Override the Solidity version (this overrides `auto_detect_solc`)
solc_version = '0.8.26'
evm_version = 'cancun'

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options
