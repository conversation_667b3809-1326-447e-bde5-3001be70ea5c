// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import {Script} from "forge-std/Script.sol";
import {console} from "forge-std/console.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

import "../src/RetailPool.sol";
import "../src/StakePool.sol";
import "../src/token/RPT.sol";
import "../src/token/StakedRPT.sol";
import "../src/strategy/ITellerMint.sol";
import "../src/strategy/ITellerRedeem.sol";
import "../src/mocks/MockUSDT.sol";
// MockEDU removed - using ITellerMint interface only

/**
 * @title Deploy Script
 * @notice Deployment script for the Retail Pool system
 * @dev Deploys all contracts using UUPS proxy pattern
 */
contract Deploy is Script {
    // Deployment addresses (to be set via environment variables)
    address public deployer;
    address public admin;
    address public fundManager;
    address public usdt;
    address public tellerMint;
    address public tellerRedeem;

    // Deployed contract addresses
    address public rptProxy;
    address public stRPTProxy;
    address public poolProxy;
    address public strategyProxy;
    address public stakePoolProxy;

    function setUp() public {
        deployer = msg.sender;

        // Set addresses from environment or use defaults for testing
        admin = vm.envOr("ADMIN_ADDRESS", deployer);
        fundManager = vm.envOr("FUND_MANAGER_ADDRESS", deployer);
        usdt = vm.envOr("USDT_ADDRESS", address(0));
        tellerMint = vm.envOr("TELLER_MINT_ADDRESS", address(0));
        tellerRedeem = vm.envOr("TELLER_REDEEM_ADDRESS", address(0));
    }

    function run() public {
        vm.startBroadcast(deployer);

        console.log("=== Retail Pool Deployment ===");
        console.log("Deployer:", deployer);
        console.log("Admin:", admin);
        console.log("Fund Manager:", fundManager);

        // Deploy mock contracts if addresses not provided
        if (usdt == address(0)) {
            console.log("\n--- Deploying Mock USDT ---");
            MockUSDT mockUSDT = new MockUSDT();
            usdt = address(mockUSDT);
            console.log("Mock USDT:", usdt);
        }

        if (tellerMint == address(0)) {
            console.log(
                "\n--- Mock EDU deployment skipped - using interface only ---"
            );
            // MockEDU removed - strategy deployment now handled separately
            // tellerMint should be set via environment variable
        }

        // Deploy implementation contracts
        console.log("\n--- Deploying Implementation Contracts ---");

        RPT rptImpl = new RPT();
        console.log("RPT Implementation:", address(rptImpl));

        StakedRPT stRPTImpl = new StakedRPT();
        console.log("StakedRPT Implementation:", address(stRPTImpl));

        RetailPool poolImpl = new RetailPool();
        console.log("RetailPool Implementation:", address(poolImpl));

        StakePool stakePoolImpl = new StakePool();
        console.log("StakePool Implementation:", address(stakePoolImpl));

        // Strategy implementation (mock for now)
        console.log("Strategy will be set separately");

        // Deploy proxy contracts with initialization
        console.log("\n--- Deploying Proxy Contracts ---");

        // Strategy proxies will be deployed separately
        address mintStrategyProxy = address(0);
        address redeemStrategyProxy = address(0);
        console.log("Mint Strategy Proxy: (not deployed in this script)");
        console.log("Redeem Strategy Proxy: (not deployed in this script)");

        // 1. Deploy RPT proxy without initialization
        console.log("Deploying RPT proxy");
        rptProxy = address(new ERC1967Proxy(address(rptImpl), ""));
        console.log("RPT Proxy:", rptProxy);

        // 2. Deploy RetailPool proxy without initialization
        console.log("Deploying RetailPool proxy");
        poolProxy = address(new ERC1967Proxy(address(poolImpl), ""));
        console.log("RetailPool Proxy:", poolProxy);

        stakePoolProxy = address(new ERC1967Proxy(address(stakePoolImpl), ""));
        console.log("StakePool Proxy:", stakePoolProxy);

        // 3. Deploy StakedRPT proxy without initialization
        console.log("Deploying StakedRPT proxy");
        stRPTProxy = address(new ERC1967Proxy(address(stRPTImpl), ""));
        console.log("StakedRPT Proxy:", stRPTProxy);

        // Initialize contracts in correct order
        console.log("\n--- Initializing Contracts ---");

        // Initialize RPT first
        RPT rpt = RPT(rptProxy);
        rpt.initialize(
            "Retail Pool Token",
            "RPT",
            admin,
            poolProxy,
            stakePoolProxy
        );
        console.log("RPT initialized");

        // Initialize StakedRPT with known addresses
        StakedRPT stRPT = StakedRPT(stRPTProxy);
        stRPT.initialize(
            "Staked Retail Pool Token",
            "stRPT",
            admin,
            stakePoolProxy
        );
        console.log("StakedRPT initialized");

        // Initialize RetailPool with all known addresses
        RetailPool pool = RetailPool(poolProxy);
        pool.initialize(
            usdt,
            rptProxy,
            mintStrategyProxy,
            redeemStrategyProxy,
            admin,
            fundManager
        );
        console.log("RetailPool initialized");

        StakePool stakePool = StakePool(stakePoolProxy);
        stakePool.initialize(rptProxy, stRPTProxy, admin);
        console.log("stakePool initialized");

        // Setup role permissions
        console.log("\n--- Setting Up Permissions ---");

        // Grant pool minter/burner roles for RPT
        rpt.grantRole(rpt.MINTER_ROLE(), poolProxy);
        rpt.grantRole(rpt.BURNER_ROLE(), poolProxy);
        console.log("Granted RPT roles to pool");

        // All other roles and addresses are already set during initialization
        console.log(
            "All contracts initialized with correct addresses and roles"
        );

        // Strategy configuration (if deployed) - skipping for now
        if (strategyProxy != address(0)) {
            console.log("Strategy configuration would be done here");
            // Grant strategy role to pool (will be set later when strategy is deployed)
            pool.grantRole(pool.STRATEGY_ROLE(), strategyProxy);
            console.log("Granted strategy role to pool");
        } else {
            console.log(
                "Strategy role will be granted separately when strategy is deployed"
            );
        }

        // Print final deployment summary
        console.log("\n=== Deployment Summary ===");
        console.log("USDT:", usdt);
        console.log("TellerMint:", tellerMint);
        console.log("TellerRedeem:", tellerRedeem);
        console.log("RPT:", rptProxy);
        console.log("StakedRPT:", stRPTProxy);
        console.log("RetailPool:", poolProxy);
        console.log("Strategy:", strategyProxy);
        console.log("Admin:", admin);
        console.log("Fund Manager:", fundManager);

        vm.stopBroadcast();

        // Write deployment addresses to file
        _writeDeploymentFile();
    }

    function _writeDeploymentFile() internal {
        string memory deploymentData = string.concat(
            "# Retail Pool Deployment Addresses\n\n",
            "USDT=",
            vm.toString(usdt),
            "\n",
            "TELLER_MINT=",
            vm.toString(tellerMint),
            "\n",
            "TELLER_REDEEM=",
            vm.toString(tellerRedeem),
            "\n",
            "RPT=",
            vm.toString(rptProxy),
            "\n",
            "STAKED_RPT=",
            vm.toString(stRPTProxy),
            "\n",
            "RETAIL_POOL=",
            vm.toString(poolProxy),
            "\n",
            "STRATEGY_EDU=",
            vm.toString(strategyProxy),
            "\n",
            "ADMIN=",
            vm.toString(admin),
            "\n",
            "FUND_MANAGER=",
            vm.toString(fundManager),
            "\n"
        );

        // Write deployment addresses to file for mainnet deployments
        try vm.writeFile("./deployments/latest.env", deploymentData) {
            console.log(
                "\nDeployment addresses saved to ./deployments/latest.env"
            );
        } catch {
            console.log(
                "\nNote: Could not write deployment file (normal in test environment)"
            );
        }

        console.log("\nDeployment completed successfully!");
    }
}
