// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import {Script} from "forge-std/Script.sol";
import {console} from "forge-std/console.sol";

import "../src/RetailPool.sol";
import "../src/token/RPT.sol";
import "../src/token/StakedRPT.sol";
import "../src/strategy/ITellerMint.sol";
import "../src/strategy/ITellerRedeem.sol";
import "../src/mocks/MockUSDT.sol";
// MockEDU removed - using ITellerMint interface only

/**
 * @title Operations Script
 * @notice Operational scripts for managing the Retail Pool system
 * @dev Various management and operational functions
 */
contract Operations is Script {
    // Contract addresses (to be loaded from environment or deployment file)
    address public usdt;
    address public rpt;
    address public stRPT;
    address public pool;
    address public strategy;
    address public mockEDU;

    function setUp() public {
        // Load addresses from environment variables
        usdt = vm.envAddress("USDT");
        rpt = vm.envAddress("RPT");
        stRPT = vm.envAddress("STAKED_RPT");
        pool = vm.envAddress("RETAIL_POOL");
        strategy = vm.envAddress("STRATEGY_EDU");
        mockEDU = vm.envOr("MOCK_EDU", address(0));
    }

    /**
     * @notice Setup initial liquidity for testing
     */
    function setupTestLiquidity() public {
        vm.startBroadcast();

        console.log("=== Setting Up Test Liquidity ===");

        MockUSDT usdtToken = MockUSDT(usdt);
        RetailPool poolContract = RetailPool(pool);

        // Mint USDT to msg.sender
        uint256 mintAmount = 100_000 * 10 ** 6; // 100k USDT
        usdtToken.mint(msg.sender, mintAmount);
        console.log("Minted", mintAmount / 10 ** 6, "USDT to", msg.sender);

        // Approve and deposit to pool
        usdtToken.approve(pool, mintAmount);

        uint256 depositAmount = 50_000 * 10 ** 6; // 50k USDT
        poolContract.deposit(depositAmount);
        console.log("Deposited", depositAmount / 10 ** 6, "USDT to pool");

        // Create some locked positions
        // Lock deposits now require separate stakeRPT calls
        poolContract.deposit(30_000 * 10 ** 6); // Total deposit
        // Note: Staking now requires separate RPT staking calls after deposit
        console.log("Created locked positions: 3m, 6m, 12m");

        vm.stopBroadcast();
    }

    /**
     * @notice Move funds to bundles (fund manager operation)
     */
    function moveToBundles() public {
        uint256 amount = vm.envUint("AMOUNT");

        vm.startBroadcast();

        console.log("=== Moving Funds to Bundles ===");
        console.log("Amount:", amount / 10 ** 6, "USDT");

        RetailPool poolContract = RetailPool(pool);
        // moveToBundles removed - strategy management now via epoch system
        console.log("Strategy management now handled via triggerEpoch()");

        console.log("Moved", amount / 10 ** 6, "USDT to bundles");
        console.log("Remaining liquidity:", poolContract.liquidity() / 10 ** 6, "USDT");

        vm.stopBroadcast();
    }

    /**
     * @notice Pull funds from bundles (fund manager operation)
     */
    function pullFromBundles() public {
        uint256 amount = vm.envUint("AMOUNT");

        vm.startBroadcast();

        console.log("=== Pulling Funds from Bundles ===");
        console.log("Amount:", amount / 10 ** 6, "USDT");

        // First approve the pool to pull USDT
        MockUSDT usdtToken = MockUSDT(usdt);
        usdtToken.approve(pool, amount);

        RetailPool poolContract = RetailPool(pool);
        // pullFromBundles removed - strategy management now via epoch system
        console.log("Strategy management now handled via triggerEpoch()");

        console.log("Pulled", amount / 10 ** 6, "USDT from bundles");
        console.log("New liquidity:", poolContract.liquidity() / 10 ** 6, "USDT");

        vm.stopBroadcast();
    }

    /**
     * @notice Report bundle interest (fund manager operation)
     */
    function reportBundleInterest() public {
        uint256 amount = vm.envUint("AMOUNT");

        vm.startBroadcast();

        console.log("=== Reporting Bundle Interest ===");
        console.log("Interest Amount:", amount / 10 ** 6, "USDT");

        RetailPool poolContract = RetailPool(pool);
        // reportBundleInterest removed - interest now calculated via strategy returns
        console.log("Bundle interest reporting not available in refactored version");

        console.log("Reported", amount / 10 ** 6, "USDT bundle interest");
        // retailNAV removed - use rptPrice() and liquidity() instead
        console.log("New RPT Price:", poolContract.rptPrice());

        vm.stopBroadcast();
    }

    /**
     * @notice Set default amount (fund manager operation)
     */
    function setDefault() public {
        uint256 amount = vm.envUint("AMOUNT");

        vm.startBroadcast();

        console.log("=== Setting Default Amount ===");
        console.log("Default Amount:", amount / 10 ** 6, "USDT");

        RetailPool poolContract = RetailPool(pool);
        // setDefaultInBundle removed - default management not available in refactored version
        console.log("Default bundle management not available in refactored version");
        console.log("Set", amount / 10 ** 6, "USDT as default");
        // retailNAV removed - use rptPrice() and liquidity() instead

        vm.stopBroadcast();
    }

    /**
     * @notice Simulate yield in mock EDU protocol
     */
    function simulateYield() public {
        require(mockEDU != address(0), "Mock EDU not configured");

        uint256 amount = vm.envUint("AMOUNT");

        vm.startBroadcast();

        console.log("=== Simulating EDU Yield ===");
        console.log("Yield Amount:", amount / 10 ** 6, "USDT");

        MockUSDT usdtToken = MockUSDT(usdt);
        // MockEDU removed - strategy yield simulation not available in this version

        // Strategy yield simulation not available - MockEDU removed
        console.log("Strategy yield simulation not available in refactored version");
        console.log("Use epoch-based management instead");

        vm.stopBroadcast();
    }

    /**
     * @notice Deposit to EDU strategy
     */
    function depositToStrategy() public {
        uint256 amount = vm.envUint("AMOUNT");

        vm.startBroadcast();

        console.log("=== Depositing to Strategy ===");
        console.log("Amount:", amount / 10 ** 6, "USDT");

        MockUSDT usdtToken = MockUSDT(usdt);
        ITellerMint strategyContract = ITellerMint(strategy);

        // Mint and approve USDT
        usdtToken.mint(msg.sender, amount);
        usdtToken.approve(strategy, amount);

        // depositToEDU removed - use ITellerMint.deposit() instead
        strategyContract.deposit(amount, address(this));

        console.log("Deposited", amount / 10 ** 6, "USDT to EDU strategy");
        // principalInYield not available in ITellerMint interface
        console.log("Strategy deposit completed");

        vm.stopBroadcast();
    }

    /**
     * @notice Begin redemption from EDU strategy
     */
    function beginRedemption() public {
        uint256 amount = vm.envUint("AMOUNT");

        vm.startBroadcast();

        console.log("=== Beginning Redemption ===");
        console.log("Amount:", amount / 10 ** 6, "USDT");

        ITellerRedeem redeemStrategyContract = ITellerRedeem(strategy);
        // beginRedeemFromEDU removed - use ITellerRedeem.redeem() instead
        bytes32 requestId = redeemStrategyContract.redeem(amount, address(this), address(this));

        console.log("Requested redemption of", amount / 10 ** 6, "USDT");
        console.logBytes32(requestId);
        // pendingRedemptions not available in ITellerMint interface
        console.log("Redemption request submitted");

        vm.stopBroadcast();
    }

    /**
     * @notice Claim from EDU strategy
     */
    function claimFromStrategy() public {
        vm.startBroadcast();

        console.log("=== Claiming from Strategy ===");

        ITellerRedeem redeemStrategyContract = ITellerRedeem(strategy);
        // claimFromEDU removed - use ITellerRedeem.claim() with request ID
        bytes32 requestId = vm.envBytes32("REQUEST_ID");
        uint256 claimed = redeemStrategyContract.claim(requestId);

        console.log("Claimed", claimed / 10 ** 6, "USDT from EDU strategy");
        // availableBalance not available in ITellerMint interface
        console.log("Claim operation completed");

        vm.stopBroadcast();
    }

    /**
     * @notice Check pool status
     */
    function checkStatus() public view {
        console.log("=== Pool Status ===");

        RetailPool poolContract = RetailPool(pool);
        RPT rptToken = RPT(rpt);

        console.log("Total Deposits:", poolContract.totalWaitDeposits() / 10 ** 6, "USDT");
        console.log("Total Wait Withdrawals:", poolContract.userWaitWithdrawals() / 10 ** 6, "USDT");
        // Legacy status fields removed in refactored version
        console.log("Liquidity:", poolContract.liquidity() / 10 ** 6, "USDT");
        console.log("RPT Price:", poolContract.rptPrice());
        console.log("RPT Total Supply:", rptToken.totalSupply() / 10 ** 18);

        if (address(strategy) != address(0)) {
            ITellerMint strategyContract = ITellerMint(strategy);
            console.log("\n=== Strategy Status ===");
            // Strategy status functions not available in ITellerMint interface
            console.log("Strategy status monitoring not available in refactored version");
            console.log("Use previewRedeem() for current strategy value");
        }
    }

    /**
     * @notice Check user positions
     */
    function checkUserPositions() public view {
        address user = vm.envAddress("USER_ADDRESS");

        console.log("=== User Positions ===");
        console.log("User:", user);

        RetailPool poolContract = RetailPool(pool);
        RPT rptToken = RPT(rpt);
        StakedRPT stRPTToken = StakedRPT(stRPT);

        console.log("RPT Balance:", rptToken.balanceOf(user) / 10 ** 18);
        console.log("stRPT Balance:", stRPTToken.balanceOf(user));
        // Note: Strokes are now tracked off-chain via events

        // Get locked positions
        StakedRPT.LockPosition[] memory positions = stRPTToken.getUserLocks(user);
        console.log("Locked Positions:", positions.length);

        for (uint256 i = 0; i < positions.length; i++) {
            console.log("Position", i, ":");
            console.log("  RPT Amount:", positions[i].amount / 10 ** 18);
            console.log("  Lock Duration:", positions[i].lockDuration, "months");
            console.log("  Lock Timestamp:", positions[i].lockTimestamp);
            console.log("  Unlock Time:", positions[i].unlockTime);
        }
    }
}
