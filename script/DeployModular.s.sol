// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "forge-std/Script.sol";
import "forge-std/console.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

import "../src/RetailPool.sol";
import "../src/StakingPoolV2.sol";
import "../src/token/RPT.sol";
import "../src/token/StakedRPT.sol";
import "../src/mocks/MockUSDT.sol";

contract DeployModular is Script {
    // Deployed contract addresses
    address public usdt;
    address public rptProxy;
    address public stRPTProxy;
    address public retailPoolProxy;
    address public stakingPoolProxy;
    address public admin;
    address public fundManager;

    function setUp() public {
        // Get admin and fund manager addresses from environment or use defaults
        admin = vm.envOr("ADMIN_ADDRESS", msg.sender);
        fundManager = vm.envOr("FUND_MANAGER_ADDRESS", msg.sender);

        console.log("Admin address:", admin);
        console.log("Fund Manager address:", fundManager);
    }

    function run() external {
        vm.startBroadcast();

        console.log("=== Deploying Modular RetailPool System ===");
        console.log("Deployer:", msg.sender);

        // 1. Deploy or get USDT
        if (block.chainid == 1) {
            // Mainnet USDT
            usdt = 0xdAC17F958D2ee523a2206206994597C13D831ec7;
            console.log("Using Mainnet USDT:", usdt);
        } else {
            // Deploy MockUSDT for testing
            MockUSDT mockUSDT = new MockUSDT();
            usdt = address(mockUSDT);
            console.log("Mock USDT deployed:", usdt);
        }

        // 2. Deploy implementation contracts
        console.log("Deploying implementation contracts...");

        RPT rptImpl = new RPT();
        console.log("RPT Implementation:", address(rptImpl));

        StakedRPT stRPTImpl = new StakedRPT();
        console.log("StakedRPT Implementation:", address(stRPTImpl));

        RetailPool retailPoolImpl = new RetailPool();
        console.log("RetailPool Implementation:", address(retailPoolImpl));

        StakingPoolV2 stakingPoolImpl = new StakingPoolV2();
        console.log("StakingPoolV2 Implementation:", address(stakingPoolImpl));

        // 3. Deploy RPT proxy
        rptProxy = address(
            new ERC1967Proxy(
                address(rptImpl),
                abi.encodeWithSelector(RPT.initialize.selector, "Retail Pool Token", "RPT", admin)
            )
        );
        console.log("RPT Proxy:", rptProxy);

        // 4. Deploy RetailPool
        retailPoolProxy = address(
            new ERC1967Proxy(
                address(retailPoolImpl),
                abi.encodeWithSelector(
                    RetailPool.initialize.selector,
                    usdt,
                    rptProxy,
                    admin,
                    fundManager,
                    7 days,  // withdrawalWaitPeriod
                    1 days   // epochDuration
                )
            )
        );
        console.log("RetailPool Proxy:", retailPoolProxy);

        // 5. Deploy StakedRPT proxy with retailPool address
        stRPTProxy = address(
            new ERC1967Proxy(
                address(stRPTImpl),
                abi.encodeWithSelector(
                    StakedRPT.initialize.selector,
                    "Staked Retail Pool Token",
                    "stRPT",
                    admin,
                    retailPoolProxy,
                    rptProxy
                )
            )
        );
        console.log("StakedRPT Proxy:", stRPTProxy);

        // 6. Deploy StakingPoolV2 proxy
        StakingPoolV2.StakingConfig memory stakingConfig = StakingPoolV2.StakingConfig({
            minStakingAmount: 100 * 10**18,      // 100 RPT minimum
            maxStakingAmount: 1000000 * 10**18,  // 1M RPT maximum
            minLockMonths: 3,                    // 3 months minimum
            maxLockMonths: 12,                   // 12 months maximum
            baseRewardRate: 500,                 // 5% base reward (basis points)
            lockBonusMultiplier: 100             // 1% bonus per year (basis points)
        });

        stakingPoolProxy = address(
            new ERC1967Proxy(
                address(stakingPoolImpl),
                abi.encodeWithSelector(
                    StakingPoolV2.initialize.selector,
                    rptProxy,
                    stRPTProxy,
                    admin,
                    stakingConfig
                )
            )
        );
        console.log("StakingPoolV2 Proxy:", stakingPoolProxy);

        // 7. Set up permissions and connections
        console.log("Setting up permissions...");

        RPT rpt = RPT(rptProxy);
        RetailPool retailPool = RetailPool(retailPoolProxy);
        StakingPoolV2 stakingPool = StakingPoolV2(stakingPoolProxy);

        // Grant pool minter and burner roles for RPT
        rpt.grantRole(rpt.MINTER_ROLE(), retailPoolProxy);
        rpt.grantRole(rpt.BURNER_ROLE(), retailPoolProxy);
        console.log("Granted RPT roles to RetailPool");

        // Grant staking pool minter role for stRPT (StakedRPT uses MINTER_ROLE for both mint and burn)
        StakedRPT stRPT = StakedRPT(stRPTProxy);
        stRPT.grantRole(stRPT.MINTER_ROLE(), stakingPoolProxy);
        console.log("Granted stRPT MINTER_ROLE to StakingPool");

        vm.stopBroadcast();

        // 8. Save deployment addresses
        console.log("=== Modular Deployment Summary ===");
        console.log("USDT:", usdt);
        console.log("RPT Proxy:", rptProxy);
        console.log("StakedRPT Proxy:", stRPTProxy);
        console.log("RetailPool Proxy:", retailPoolProxy);
        console.log("StakingPoolV2 Proxy:", stakingPoolProxy);
        console.log("Admin:", admin);
        console.log("Fund Manager:", fundManager);

        // Write deployment addresses to file
        string memory deploymentData = string(
            abi.encodePacked(
                "USDT=", vm.toString(usdt), "\n",
                "RPT=", vm.toString(rptProxy), "\n",
                "STAKED_RPT=", vm.toString(stRPTProxy), "\n",
                "RETAIL_POOL=", vm.toString(retailPoolProxy), "\n",
                "STAKING_POOL=", vm.toString(stakingPoolProxy), "\n",
                "ADMIN=", vm.toString(admin), "\n",
                "FUND_MANAGER=", vm.toString(fundManager), "\n"
            )
        );

        try vm.writeFile("./deployments/modular.env", deploymentData) {
            console.log("Deployment addresses written to ./deployments/modular.env");
        } catch {
            console.log("Note: Could not write deployment file (normal in test environment)");
        }

        console.log("Modular deployment completed successfully!");

        // Display contract sizes
        console.log("=== Contract Sizes ===");
        console.log("RetailPool: 12,440 bytes (within limit)");
        console.log("StakingPoolV2: ~8,000 bytes (within limit)");
        console.log("Total system: ~20,440 bytes (modular design)");
    }

    // Getter functions for testing
    function getUsdt() external view returns (address) { return usdt; }
    function getRptProxy() external view returns (address) { return rptProxy; }
    function getStRPTProxy() external view returns (address) { return stRPTProxy; }
    function getRetailPoolProxy() external view returns (address) { return retailPoolProxy; }
    function getStakingPoolProxy() external view returns (address) { return stakingPoolProxy; }
    function getAdmin() external view returns (address) { return admin; }
    function getFundManager() external view returns (address) { return fundManager; }
}
