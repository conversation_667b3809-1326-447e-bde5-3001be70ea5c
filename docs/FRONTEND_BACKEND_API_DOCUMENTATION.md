# Retail Pool & Stake Pool 合约文档

## 📋 概述

本文档详细说明了 RetailPool 和 StakePool 合约的前后端接口，包括用户（User）和基金管理员（Fund Manager）的所有可用操作。

**合约分工**：

- **RetailPool**: 负责USDT存取款、流动性管理
- **StakePool**: 负责RPT质押获得stRPT的功能

## 🏗️ 合约架构

### 核心角色

- **User**: 普通用户，可以存款、提取
- **Fund Manager**: 基金管理员，具有 `FUND_MANAGER_ROLE` 权限
- **Admin**: 系统管理员，具有 `DEFAULT_ADMIN_ROLE` 权限
- **Pauser**: 暂停管理员，具有 `PAUSER_ROLE` 权限

### 操作模式

- **BATCH_MODE**: 批量模式，操作需等待 epoch 触发

## 👤 用户（User）操作流程

### 1. 存款流程

#### 1.1 存款操作

```solidity
function deposit(uint256 amount) external
```

**参数**:

- `amount`: USDT 数量（6位小数）

**前置条件**:

- 用户需要先 approve USDT 给合约
- 合约未暂停

**流程**:

1. 用户调用 `usdt.approve(poolAddress, amount)`
2. 用户调用 `pool.deposit(amount)`
3. 合约根据当前 RPT 价格计算并铸造 RPT
4. 资金等待 epoch 处理

**事件**:

```solidity
event USDTDeposited(address indexed user, uint256 usdtAmount, uint256 rptAmount);
```

#### 1.2 查询 RPT 价格

```solidity
function rptPrice() public view returns (uint256)
```

**返回**: RPT/USDT 价格（18位小数）

### 2. 提取流程

#### 2.1 请求提取

```solidity
function requestWithdraw(uint256 rptAmount) external returns (uint256 requestId)
```

**参数**:

- `rptAmount`: 要燃烧的 RPT 数量（18位小数）

**返回**:

- `requestId`: 提取请求ID

**流程**:

1. 合约燃烧用户的 RPT
2. 计算对应的 USDT 数量
3. 创建请求，等待 epoch 处理

**事件**:

```solidity
event WithdrawRequested(address indexed user, uint256 indexed requestId, uint256 rptAmount, uint256 usdtAmount);
```

#### 2.2 查询提取状态

```solidity
function canClaimWithdraw(uint256 requestId) external view returns (bool canClaim, uint256 timeRemaining)
```

**参数**:

- `requestId`: 提取请求ID

**返回**:

- `canClaim`: 是否可以领取
- `timeRemaining`: 剩余等待时间（秒）

#### 2.3 领取提取

```solidity
function claimWithdraw(uint256 requestId) external
```

**参数**:

- `requestId`: 提取请求ID

**前置条件**:

- 等待期已过（默认7天）
- 策略赎回已完成（即时模式）

**事件**:

```solidity
event WithdrawClaimed(address indexed user, uint256 indexed requestId, uint256 rptAmount, uint256 usdtAmount);
```

#### 2.4 即时提取（BATCH_MODE 专用）

```solidity
function withdrawImmediately(uint256 rptAmount) external returns (bool success)
```

**参数**:

- `rptAmount`: 要燃烧的 RPT 数量（18位小数）

**返回**:

- `success`: 是否成功立即提取

**前置条件**:

- 当前为 BATCH_MODE
- 即时池有足够资金
- 合约未暂停

**流程**:

1. 检查操作模式和资金可用性
2. 燃烧用户的 RPT
3. 从即时池扣减 USDT
4. 立即转账 USDT 给用户

**事件**:

```solidity
event ImmediateWithdrawCompleted(address indexed user, uint256 rpt, uint256 usdt);
```

**使用场景**:

- 用户希望立即提取刚存入的资金
- 避免 7 天等待期
- 只在即时池有足够流动性时可用

#### 2.5 查询即时提取可用性

```solidity
function canWithdrawImmediately(uint256 rptAmount) external view returns (bool canWithdraw)
```

**参数**:

- `rptAmount`: 要提取的 RPT 数量（18位小数）

**返回**:

- `canWithdraw`: 是否可以立即提取

**检查条件**:

- 当前为 BATCH_MODE
- 即时池资金充足
- RPT 金额有效

#### 2.6 查询用户提取请求

```solidity
mapping(address => uint256[]) public userWithdrawRequestIds;
```

### 3. 质押流程

#### 3.1 质押 RPT

```solidity
function stakeRPT(uint256 rptAmount, uint256 months) external
```

**参数**:

- `rptAmount`: 质押的 RPT 数量（18位小数）
- `months`: 锁定期（3、6或12个月）

**流程**:

1. 燃烧用户的 RPT
2. 铸造对应的 stRPT
3. 设置锁定期

**事件**:

```solidity
event RPTStaked(address indexed user, uint256 rptAmount, uint256 months, uint256 positionId);
```

#### 3.2 解质押 RPT

```solidity
function unstakeRPT(uint256 amount) external
```

**参数**:

- `amount`: 解质押的 stRPT 数量（18位小数）

**前置条件**:

- 锁定期已到期

### 4. 查询函数

#### 4.1 流动性查询

```solidity
function liquidity() public view returns (uint256)
function totalLiquidity() public view returns (uint256)
```

#### 4.2 提取请求详情

```solidity
mapping(uint256 => WithdrawRequest) public withdrawRequests;
```

**结构体**:

```solidity
struct WithdrawRequest {
    address requester;
    uint256 rptAmount;
    uint256 usdtAmount;
    uint256 requestTime;
    bool claimed;
    uint256 strategyRequestId;
    RequestType requestType;
}
```

## 💼 基金管理员（Fund Manager）操作流程

### 1. 资金注入

#### 1.1 注入资金

```solidity
function injectFunds(uint256 usdtAmount) external onlyRole(FUND_MANAGER_ROLE)
```

**参数**:

- `usdtAmount`: 注入的 USDT 数量（6位小数）

**前置条件**:

- 管理员需要先 approve USDT 给合约

**流程**:

1. **BATCH_MODE**: 资金等待 epoch 处理
2. **INSTANT_MODE**: 资金立即投入策略

**事件**:

```solidity
event FundsInjected(address indexed admin, uint256 amount);
```

### 2. 管理员提取流程

#### 2.1 请求提取

```solidity
function adminRequestWithdraw(uint256 usdtAmount) external onlyRole(FUND_MANAGER_ROLE) returns (uint256 requestId)
```

**参数**:

- `usdtAmount`: 要提取的 USDT 数量（6位小数）

**返回**:

- `requestId`: 提取请求ID

**前置条件**:

- 有足够的可用流动性

**事件**:

```solidity
event AdminWithdrawRequested(address indexed admin, uint256 indexed requestId, uint256 usdtAmount);
```

#### 2.2 查询提取状态

```solidity
function canClaimWithdraw(uint256 requestId) external view returns (bool canClaim, uint256 timeRemaining)
```

#### 2.3 领取提取

```solidity
function claimWithdraw(uint256 requestId) external
```

**事件**:

```solidity
event AdminWithdrawClaimed(address indexed admin, uint256 indexed requestId, uint256 usdtAmount);
```

#### 2.4 管理员即时提取（BATCH_MODE 专用）

```solidity
function adminWithdrawImmediately(uint256 usdtAmount) external onlyRole(FUND_MANAGER_ROLE) returns (bool success)
```

**参数**:

- `usdtAmount`: 要提取的 USDT 数量（6位小数）

**返回**:

- `success`: 是否成功立即提取

**前置条件**:

- 调用者具有 `FUND_MANAGER_ROLE` 权限
- 当前为 BATCH_MODE
- 即时池有足够资金
- 符合管理员流动性限制
- 合约未暂停

**流程**:

1. 检查权限和操作模式
2. 验证即时池资金和流动性限制
3. 从即时池扣减 USDT
4. 立即转账 USDT 给管理员

**事件**:

```solidity
event AdminImmediateWithdrawCompleted(address indexed admin, uint256 usdt);
```

**使用场景**:

- 管理员需要快速响应流动性需求
- 避免传统提取流程的等待时间
- 紧急资金调配

#### 2.5 查询管理员即时提取可用性

```solidity
function canAdminWithdrawImmediately(uint256 usdtAmount) external view returns (bool canWithdraw)
```

**参数**:

- `usdtAmount`: 要提取的 USDT 数量（6位小数）

**返回**:

- `canWithdraw`: 是否可以立即提取

**检查条件**:

- 当前为 BATCH_MODE
- 即时池资金充足
- 符合管理员流动性限制
- USDT 金额有效

#### 2.6 查询管理员提取请求

```solidity
function getAdminWithdrawRequests(address admin) external view returns (uint256[] memory)
mapping(address => uint256[]) public adminWithdrawRequestIds;
```

### 3. Epoch 管理

#### 3.1 触发 Epoch

```solidity
function triggerEpoch() external
```

- [ ] **功能**:

- **BATCH_MODE**: 处理所有待处理的批量操作
- **INSTANT_MODE**: 清理已完成的策略赎回

**前置条件**:

- 距离上次 epoch 已过足够时间

**事件**:

```solidity
event EpochTriggered(uint256 timestamp);
```

## 🔄 统一接口

### 统一提取领取

```solidity
function claimWithdraw(uint256 requestId) external
```

**功能**: 自动识别请求类型并应用相应权限

- 用户请求：任何人都可以领取自己的请求
- 管理员请求：需要 FUND_MANAGER_ROLE

## 📊 前端集成建议

### 1. 用户界面流程

#### 存款流程

```javascript
// 1. 检查授权
const allowance = await usdt.allowance(userAddress, poolAddress);
if (allowance < amount) {
    await usdt.approve(poolAddress, amount);
}

// 2. 执行存款
await pool.deposit(amount);

// 3. 监听事件
pool.on('USDTDeposited', (user, usdtAmount, rptAmount) => {
    // 更新UI
});
```

#### 提取流程

```javascript
// 智能提取流程（优先尝试即时提取）
const smartWithdraw = async (rptAmount) => {
    // 1. 检查是否可以立即提取
    const canImmediate = await pool.canWithdrawImmediately(rptAmount);

    if (canImmediate) {
        // 2. 执行即时提取
        const success = await pool.withdrawImmediately(rptAmount);
        if (success) {
            console.log("提取已完成，资金已到账！");
            return { type: 'immediate', success: true };
        }
    }

    // 3. 回退到传统提取流程
    const requestId = await pool.requestWithdraw(rptAmount);
    console.log("提取请求已提交，请等待处理");
    return { type: 'traditional', requestId };
};

// 传统提取流程
const traditionalWithdraw = async (rptAmount) => {
    // 1. 请求提取
    const requestId = await pool.requestWithdraw(rptAmount);

    // 2. 定期检查状态
    const checkStatus = async () => {
        const [canClaim, timeRemaining] = await pool.canClaimWithdraw(requestId);
        if (canClaim) {
            // 显示领取按钮
        } else {
            // 显示倒计时
        }
    };

    // 3. 领取
    await pool.claimWithdraw(requestId);
};
```

### 2. 管理员界面流程

#### 资金管理

```javascript
// 注入资金
await pool.injectFunds(amount);

// 智能管理员提取流程（优先尝试即时提取）
const adminSmartWithdraw = async (usdtAmount) => {
    // 1. 检查是否可以立即提取
    const canImmediate = await pool.canAdminWithdrawImmediately(usdtAmount);

    if (canImmediate) {
        // 2. 执行即时提取
        const success = await pool.adminWithdrawImmediately(usdtAmount);
        if (success) {
            console.log("管理员提取已完成！");
            return { type: 'immediate', success: true };
        }
    }

    // 3. 回退到传统管理员提取流程
    const requestId = await pool.adminRequestWithdraw(usdtAmount);
    console.log("管理员提取请求已提交，请等待处理");
    return { type: 'traditional', requestId };
};

// 传统管理员提取流程
const traditionalAdminWithdraw = async (usdtAmount) => {
    // 请求提取
    const requestId = await pool.adminRequestWithdraw(usdtAmount);

    // 领取提取
    await pool.claimWithdraw(requestId);
};
```

#### Epoch 管理

```javascript
// 触发 epoch
await pool.triggerEpoch();

// 监听 epoch 事件
pool.on('EpochTriggered', (timestamp) => {
    // 更新状态
});
```

### 3. 状态查询

#### 实时数据

```javascript
// RPT 价格
const price = await pool.rptPrice();

// 流动性,包含当前 pool 中的自由流动性和 solen 中可提取的流动性
const liquidity = await pool.liquidity();

// 即时池资金（BATCH_MODE 专用）
const immediatePool = await pool.totalWaitDeposits();

// 用户提取请求
const userRequests = await pool.userWithdrawRequestIds(userAddress);

// 管理员提取请求
const adminRequests = await pool.getAdminWithdrawRequests(adminAddress);
```

## ⚠️ 重要注意事项

### 1. 权限控制

- 确保前端正确检查用户角色
- 管理员功能需要验证 FUND_MANAGER_ROLE

### 2. 操作模式

- 根据当前操作模式调整UI提示
- 批量模式需要等待 epoch 处理

### 3. 错误处理

- 处理常见错误：`InsufficientLiquidity`, `InvalidAmount`, `WaitPeriodNotCompleted`
- 提供用户友好的错误信息

### 4. 事件监听

- 监听关键事件以更新UI状态
- 处理网络重连和事件重放

## 🚨 错误处理

### 常见错误类型

```solidity
error InvalidAmount();                    // 金额为0或无效
error InsufficientLiquidity();           // 流动性不足
error InsufficientStrategyLiquidity();   // 策略流动性不足
error InvalidRequestId();                // 无效的请求ID
error RequestAlreadyClaimed();           // 请求已被领取
error WaitPeriodNotCompleted();          // 等待期未完成
error Unauthorized();                    // 权限不足
error StrategyRequestNotReady();         // 策略请求未就绪
error EpochTooEarly();                   // Epoch触发过早
error InvalidOperationMode();            // 无效的操作模式
error PositionNotMatured();              // 质押位置未到期
```

## 📡 事件定义

### 用户相关事件

```solidity
event USDTDeposited(address indexed user, uint256 usdtAmount, uint256 rptAmount);
event WithdrawRequested(address indexed user, uint256 indexed requestId, uint256 rptAmount, uint256 usdtAmount);
event WithdrawClaimed(address indexed user, uint256 indexed requestId, uint256 rptAmount, uint256 usdtAmount);
event ImmediateWithdrawCompleted(address indexed user, uint256 rpt, uint256 usdt);
event RPTStaked(address indexed user, uint256 rptAmount, uint256 months, uint256 positionId);
event RPTUnstaked(address indexed user, uint256 amount, uint256 positionId);
```

### 管理员相关事件

```solidity
event FundsInjected(address indexed admin, uint256 amount);
event AdminWithdrawRequested(address indexed admin, uint256 indexed requestId, uint256 usdtAmount);
event AdminWithdrawClaimed(address indexed admin, uint256 indexed requestId, uint256 usdtAmount);
event AdminImmediateWithdrawCompleted(address indexed admin, uint256 usdt);
event EpochTriggered(uint256 timestamp);
```

### 策略相关事件

```solidity
event StrategyDeposited(uint256 amount, uint256 shares);
event StrategyRedeemRequested(uint256 indexed internalId, bytes32 indexed tellerRequestId, uint256 amount);
event StrategyRedeemClaimed(uint256 indexed internalId, uint256 amount);
```

## 🔧 配置参数

### 可查询的配置

```solidity
uint256 public withdrawalWaitPeriod;      // 提取等待期（默认7天）
uint256 public epochDuration;             // Epoch间隔（默认24小时）
OperationMode public operationMode;       // 当前操作模式
```

### 查询配置示例

```javascript
const config = {
    withdrawalWaitPeriod: await pool.withdrawalWaitPeriod(),
    epochDuration: await pool.epochDuration(),
    operationMode: await pool.operationMode(),
};
```

## 📊 数据格式说明

### 金额精度

- **USDT**: 6位小数 (1 USDT = 1,000,000 wei)
- **RPT**: 18位小数 (1 RPT = 1,000,000,000,000,000,000 wei)
- **价格**: 18位小数 (1.5 = 1,500,000,000,000,000,000)
