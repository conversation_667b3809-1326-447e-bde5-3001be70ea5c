// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import "./helpers/TestHelper.sol";

/**
 * @title RetailPoolTest
 *  Test Suite
 * @notice Comprehensive tests for the refactored RetailPool contract
 */
contract RetailPoolTest is TestHelper {
    // Test events
    event WithdrawRequested(address indexed user, uint256 indexed requestId, uint256 rpt, uint256 usdt);
    event WithdrawClaimed(address indexed user, uint256 indexed requestId, uint256 rpt, uint256 usdt);
    event EpochTriggered(address indexed trigger, uint256 epochTime, int256 netAmount);
    event StrategyDeposited(uint256 amount, uint256 shares);
    event StrategyRedeemRequested(uint256 indexed internalId, uint256 indexed tellerRequestId, uint256 amount);

    function setUp() public override {
        super.setUp();
        // Set initial epoch time to current time
        vm.warp(1000);
    }

    // ================== Basic Functionality Tests ==================

    function testInitialState() public view {
        assertEq(address(pool.usdt()), address(usdt));
        assertEq(address(pool.rpt()), address(rpt));
        assertEq(address(pool.mintStrategy()), address(mintStrategy));
        assertEq(address(pool.redeemStrategy()), address(redeemStrategy));

        // Check initial values
        assertEq(pool.totalWaitDeposits(), 0);
        assertEq(pool.userWaitWithdrawals(), 0);
        assertEq(pool.strategyShares(), 0);
        assertEq(pool.withdrawalWaitPeriod(), 7 days);
        assertEq(pool.epochDuration(), 24 hours);

        // Pool is now always in BATCH_MODE

        // Initial RPT price should be 1:1
        assertEq(pool.rptPrice(), 1e18);
    }

    function testSimpleDeposit() public {
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT
        uint256 expectedRPT = 1000 * 1e18; // 1000 RPT (1:1 initially)

        vm.startPrank(alice);
        pool.deposit(depositAmount);
        vm.stopPrank();

        assertEq(rpt.balanceOf(alice), expectedRPT);
        // In batch mode (default), funds wait for epoch
        assertEq(pool.totalWaitDeposits(), depositAmount);
        assertEq(pool.strategyShares(), 0); // No immediate staking in batch mode
        assertEq(usdt.balanceOf(address(pool)), depositAmount); // USDT stays in contract
    }

    function testMultipleDeposits() public {
        uint256 aliceDeposit = 1000 * 1e6;
        uint256 bobDeposit = 500 * 1e6;

        vm.prank(alice);
        pool.deposit(aliceDeposit);

        vm.prank(bob);
        pool.deposit(bobDeposit);

        // In batch mode (default), all funds wait for epoch
        assertEq(pool.totalWaitDeposits(), aliceDeposit + bobDeposit);
        assertEq(pool.strategyShares(), 0); // No immediate staking in batch mode
        assertEq(rpt.balanceOf(alice), 1000 * 1e18);
        assertEq(rpt.balanceOf(bob), 500 * 1e18);
        assertEq(usdt.balanceOf(address(pool)), aliceDeposit + bobDeposit); // USDT stays in contract
    }

    // ================== Two-Step Withdrawal Tests ==================

    function testWithdrawRequest() public {
        uint256 depositAmount = 1000 * 1e6;
        uint256 withdrawRPT = 500 * 1e18;

        // Alice deposits first
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Alice requests withdrawal
        vm.prank(alice);
        vm.expectEmit(true, true, false, true);
        emit WithdrawRequested(alice, 1, withdrawRPT, 500 * 1e6);
        uint256 requestId = pool.requestWithdraw(withdrawRPT);

        assertEq(requestId, 1);
        assertEq(rpt.balanceOf(alice), 500 * 1e18); // Half burned

        // In batch mode (default), should track wait withdrawals
        assertEq(pool.userWaitWithdrawals(), 500 * 1e6);
        assertEq(pool.strategyShares(), 0); // No strategy interaction in batch mode

        // Check request details - no strategy request in batch mode
        (address requester, uint256 rptAmount, uint256 usdtAmount, uint256 requestTime, bool claimed, uint256 strategyRequestId, RetailPool.RequestType requestType) =
            pool.withdrawRequests(requestId);

        assertEq(requester, alice);
        assertEq(rptAmount, withdrawRPT);
        assertEq(usdtAmount, 500 * 1e6);
        assertEq(requestTime, block.timestamp);
        assertFalse(claimed);
        assertEq(strategyRequestId, 0); // No strategy request in batch mode
    }

    function testCannotClaimWithdrawTooEarly() public {
        uint256 depositAmount = 1000 * 1e6;

        vm.startPrank(alice);
        pool.deposit(depositAmount);
        uint256 requestId = pool.requestWithdraw(500 * 1e18);

        // Try to claim immediately - should fail
        vm.expectRevert(RetailPool.WaitPeriodNotCompleted.selector);
        pool.claimWithdraw(requestId);
        vm.stopPrank();
    }

    function testSuccessfulWithdrawClaim() public {
        uint256 depositAmount = 1000 * 1e6;

        vm.startPrank(alice);
        pool.deposit(depositAmount);
        uint256 requestId = pool.requestWithdraw(500 * 1e18);

        // Fast forward past both wait period and strategy cooldown period
        vm.warp(block.timestamp + 8 days);

        uint256 aliceUSDTBefore = usdt.balanceOf(alice);

        vm.expectEmit(true, true, false, true);
        emit WithdrawClaimed(alice, requestId, 500 * 1e18, 500 * 1e6);
        pool.claimWithdraw(requestId);
        vm.stopPrank();

        // Check results
        assertEq(usdt.balanceOf(alice), aliceUSDTBefore + 500 * 1e6);

        (,,,, bool claimed,,) = pool.withdrawRequests(requestId);
        assertTrue(claimed);
    }

    function testCannotClaimTwice() public {
        uint256 depositAmount = 1000 * 1e6;

        vm.startPrank(alice);
        pool.deposit(depositAmount);
        uint256 requestId = pool.requestWithdraw(500 * 1e18);

        vm.warp(block.timestamp + 8 days);
        pool.claimWithdraw(requestId);

        // Try to claim again - should fail
        vm.expectRevert(RetailPool.RequestAlreadyClaimed.selector);
        pool.claimWithdraw(requestId);
        vm.stopPrank();
    }

    function testCanClaimWithdrawStatus() public {
        uint256 depositAmount = 1000 * 1e6;

        vm.startPrank(alice);
        pool.deposit(depositAmount);
        uint256 requestId = pool.requestWithdraw(500 * 1e18);
        vm.stopPrank();

        // Initially should not be claimable
        (bool canClaim, uint256 timeRemaining) = pool.canClaimWithdraw(requestId);
        assertFalse(canClaim);
        assertEq(timeRemaining, 7 days);

        // Fast forward partially
        vm.warp(block.timestamp + 3 days);
        (canClaim, timeRemaining) = pool.canClaimWithdraw(requestId);
        assertFalse(canClaim);
        assertEq(timeRemaining, 4 days);

        // After wait period
        vm.warp(block.timestamp + 5 days);
        (canClaim, timeRemaining) = pool.canClaimWithdraw(requestId);
        assertTrue(canClaim);
        assertEq(timeRemaining, 0);
    }

    function testGetUserWithdrawRequests() public {
        uint256 depositAmount = 1000 * 1e6;

        vm.startPrank(alice);
        pool.deposit(depositAmount);

        uint256 requestId1 = pool.requestWithdraw(200 * 1e18);
        uint256 requestId2 = pool.requestWithdraw(300 * 1e18);
        vm.stopPrank();

        uint256[] memory requests = pool.getUserWithdrawRequests(alice);
        assertEq(requests.length, 2);
        assertEq(requests[0], requestId1);
        assertEq(requests[1], requestId2);
    }

    // ================== Epoch Management Tests ==================

    function testTriggerEpochTooEarly() public {
        vm.expectRevert(RetailPool.EpochTooEarly.selector);
        pool.triggerEpoch();
    }

    function testTriggerEpochWithNetDeposits() public {
        uint256 depositAmount = 1000 * 1e6;

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Wait for epoch duration
        vm.warp(block.timestamp + 25 hours);

        // Mock mint strategy to return shares
        vm.mockCall(
            address(mintStrategy),
            abi.encodeWithSelector(mintStrategy.deposit.selector),
            abi.encode(100) // Return 100 shares
        );

        vm.expectEmit(true, false, false, false);
        emit EpochTriggered(address(this), block.timestamp, int256(depositAmount));

        pool.triggerEpoch();

        assertEq(pool.strategyShares(), 100);
        assertEq(pool.lastEpochTime(), block.timestamp);
    }

    function testTriggerEpochWithNetWithdrawals() public {
        // Setup initial deposits and withdrawals to create net withdrawal scenario
        vm.startPrank(alice);
        pool.deposit(1000 * 1e6); // Alice deposits 1000 USDT
        vm.stopPrank();

        // First epoch: establish strategy position
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch(); // This moves Alice's deposit to strategy

        // Now create net withdrawal scenario: more withdrawals than new deposits
        vm.startPrank(alice);
        pool.requestWithdraw(800 * 1e18); // Alice requests 800 USDT withdrawal
        vm.stopPrank();

        vm.prank(bob);
        pool.deposit(200 * 1e6); // Bob deposits only 200 USDT

        // Net amount = 200 new deposits - 800 withdrawals = -600 USDT (negative)

        vm.warp(block.timestamp + 25 hours);

        // Mock redeem strategy calls for redemption
        vm.mockCall(
            address(redeemStrategy),
            abi.encodeWithSelector(redeemStrategy.previewRedeem.selector),
            abi.encode(1000 * 1e6) // Strategy has 1000 USDT available
        );

        vm.mockCall(
            address(redeemStrategy),
            abi.encodeWithSelector(redeemStrategy.redeem.selector),
            abi.encode(1) // Return request ID 1
        );

        uint256 sharesBefore = pool.strategyShares();
        pool.triggerEpoch();
        uint256 sharesAfter = pool.strategyShares();

        // With net withdrawals (-600 USDT), strategy should be redeemed, reducing shares
        assertLt(sharesAfter, sharesBefore, "Strategy shares should decrease with net withdrawals");
    }

    // ================== Price Calculation Tests ==================

    function testRPTPriceWithStrategyValue() public {
        uint256 depositAmount = 1000 * 1e6;

        vm.prank(alice);
        pool.deposit(depositAmount);

        // Initial price should be 1:1
        assertEq(pool.rptPrice(), 1e18);

        // Fast forward time to allow epoch trigger
        vm.warp(block.timestamp + 25 hours);

        // Trigger epoch - this will invest the 1000 USDT deposits into strategy
        pool.triggerEpoch();

        // Sync strategy shares between mint and redeem
        syncStrategyShares();

        // Debug sync values
        console.log("MockTellerMint totalShares:", mockMintStrategy.totalShares());
        console.log("MockTellerMint totalAssets:", mockMintStrategy.totalAssets());
        console.log("MockTellerRedeem totalShares:", mockRedeemStrategy.totalShares());
        console.log("MockTellerRedeem totalAssets:", mockRedeemStrategy.totalAssets());

        // Verify funds were moved to strategy
        uint256 currentShares = pool.strategyShares();
        uint256 totalWaitDeposits = pool.totalWaitDeposits();
        assertEq(totalWaitDeposits, 0, "Wait deposits should be processed");
        assertGt(currentShares, 0, "Strategy shares should be set");

        // Now simulate strategy gains by setting total assets in mock strategy
        // MockTellerRedeem uses totalAssets for previewRedeem calculation
        mockRedeemStrategy.setTotalAssets(1200 * 1e6);

        // Debug strategy value calculation
        uint256 contractBalance = usdt.balanceOf(address(pool));
        console.log("Contract balance:");
        console.logUint(contractBalance);
        console.log("Strategy shares:");
        console.logUint(currentShares);

        // Check strategy preview manually
        uint256 strategyValue = redeemStrategy.previewRedeem(currentShares);
        console.log("Strategy preview value:");
        console.logUint(strategyValue);

        uint256 newPrice = pool.rptPrice();
        console.log("New RPT price:");
        console.logUint(newPrice);

        // For this test, let's verify the price logic works correctly
        // After setting strategy gains (1000 -> 1200 USDT), the price should increase
        // Expected: 1200 USDT / 1000 USDT = 1.2x = 1.2e18
        assertEq(newPrice, 1200000000000000000, "Price should reflect strategy gains: 1.2x");
    }

    function testLiquidityCalculation() public {
        uint256 depositAmount = 1000 * 1e6;

        vm.prank(alice);
        pool.deposit(depositAmount);

        // Liquidity should equal USDT balance only
        assertEq(pool.liquidity(), depositAmount);

        // Even with strategy value, liquidity should only count immediate balance
        vm.mockCall(
            address(redeemStrategy),
            abi.encodeWithSelector(redeemStrategy.previewRedeem.selector),
            abi.encode(500 * 1e6)
        );

        assertEq(pool.liquidity(), depositAmount); // Unchanged
    }

    // ================== Admin Functions Tests ==================

    function testSetWithdrawalWaitPeriod() public {
        uint256 newPeriod = 10 days;

        vm.prank(owner);
        pool.setWithdrawalWaitPeriod(newPeriod);

        assertEq(pool.withdrawalWaitPeriod(), newPeriod);
    }

    function testSetEpochDuration() public {
        uint256 newDuration = 12 hours;

        vm.prank(owner);
        pool.setEpochDuration(newDuration);

        assertEq(pool.epochDuration(), newDuration);
    }

    function testOnlyAdminCanChangeConfig() public {
        vm.prank(alice);
        vm.expectRevert();
        pool.setWithdrawalWaitPeriod(10 days);

        vm.prank(alice);
        vm.expectRevert();
        pool.setEpochDuration(12 hours);
    }

    // ================== Access Control Tests ==================

    function testRoleBasedAccess() public {
        assertTrue(pool.hasRole(pool.DEFAULT_ADMIN_ROLE(), owner));
        assertTrue(pool.hasRole(pool.FUND_MANAGER_ROLE(), fundManager));
        assertTrue(pool.hasRole(pool.STRATEGY_ROLE(), address(mintStrategy)));
        assertTrue(pool.hasRole(pool.STRATEGY_ROLE(), address(redeemStrategy)));
        assertTrue(pool.hasRole(pool.PAUSER_ROLE(), owner));
        assertTrue(pool.hasRole(pool.UPGRADER_ROLE(), owner));
    }

    function testPauseUnpause() public {
        // Owner can pause
        vm.prank(owner);
        pool.pause();

        // Deposits should fail when paused
        vm.prank(alice);
        vm.expectRevert();
        pool.deposit(1000 * 1e6);

        // Owner can unpause
        vm.prank(owner);
        pool.unpause();

        // Should work after unpause
        vm.prank(alice);
        pool.deposit(1000 * 1e6);
    }

    // ================== Edge Cases and Error Tests ==================
    function testInsufficientLiquidityForClaim() public {
        vm.startPrank(alice);
        pool.deposit(1000 * 1e6);
        uint256 requestId = pool.requestWithdraw(500 * 1e18);
        vm.stopPrank();

        // Remove liquidity somehow (simulate strategy deposit)
        vm.store(address(usdt), keccak256(abi.encode(address(pool), uint256(0))), bytes32(uint256(100 * 1e6)));

        vm.warp(block.timestamp + 8 days);

        vm.prank(alice);
        vm.expectRevert(RetailPool.InsufficientLiquidity.selector);
        pool.claimWithdraw(requestId);
    }

    function testInvalidRequestId() public {
        vm.prank(alice);
        vm.expectRevert(RetailPool.InvalidRequestId.selector);
        pool.claimWithdraw(999); // Non-existent request ID
    }

    // ================== Integration Tests ==================

    function testCompleteUserJourney() public {
        uint256 initialDeposit = 2000 * 1e6;

        // 1. Alice deposits
        vm.startPrank(alice);
        pool.deposit(initialDeposit);
        assertEq(rpt.balanceOf(alice), 2000 * 1e18);

        // 3. Alice requests withdrawal of remaining RPT
        uint256 requestId = pool.requestWithdraw(500 * 1e18);
        assertEq(rpt.balanceOf(alice), 500 * 1e18);

        // 4. Wait and claim withdrawal
        // Note: After staking 1000 RPT (burned), remaining 1000 RPT are backed by 2000 USDT
        // So price = 2000/1000 = 2 USDT per RPT, hence 500 RPT = 1000 USDT
        vm.warp(block.timestamp + 8 days);
        uint256 usdtBefore = usdt.balanceOf(alice);
        pool.claimWithdraw(requestId);
        assertEq(usdt.balanceOf(alice), usdtBefore + 1000 * 1e6);
    }
}
