// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "forge-std/Test.sol";
import "../script/Deploy.s.sol";
import "../src/RetailPool.sol";
import "../src/token/RPT.sol";
import "../src/token/StakedRPT.sol";

contract DeployTest is Test {
    Deploy deployScript;

    function setUp() public {
        deployScript = new Deploy();
        deployScript.setUp();
    }

    function testDeployment() public {
        // Run the deployment script
        deployScript.run();

        // Get deployed addresses
        address rptProxy = deployScript.rptProxy();
        address stRPTProxy = deployScript.stRPTProxy();
        address poolProxy = deployScript.poolProxy();
        address usdt = deployScript.usdt();
        address tellerMint = deployScript.tellerMint();
        address tellerRedeem = deployScript.tellerRedeem();
        address admin = deployScript.admin();
        address fundManager = deployScript.fundManager();

        // Verify contracts are deployed
        assertTrue(rptProxy != address(0), "RPT proxy not deployed");
        assertTrue(stRPTProxy != address(0), "StakedRPT proxy not deployed");
        assertTrue(poolProxy != address(0), "RetailPool proxy not deployed");
        assertTrue(usdt != address(0), "USDT not deployed");

        // Test contract initialization
        RPT rpt = RPT(rptProxy);
        StakedRPT stRPT = StakedRPT(stRPTProxy);
        RetailPool pool = RetailPool(poolProxy);

        // Verify RPT initialization
        assertEq(rpt.name(), "Retail Pool Token");
        assertEq(rpt.symbol(), "RPT");
        assertTrue(rpt.hasRole(rpt.DEFAULT_ADMIN_ROLE(), admin));
        assertTrue(rpt.hasRole(rpt.MINTER_ROLE(), poolProxy));
        assertTrue(rpt.hasRole(rpt.BURNER_ROLE(), poolProxy));

        // Verify StakedRPT initialization
        assertEq(stRPT.name(), "Staked Retail Pool Token");
        assertEq(stRPT.symbol(), "stRPT");
        assertTrue(stRPT.hasRole(stRPT.DEFAULT_ADMIN_ROLE(), admin));
        assertTrue(stRPT.hasRole(stRPT.MINTER_ROLE(), poolProxy));

        // Verify RetailPool initialization
        assertTrue(pool.hasRole(pool.DEFAULT_ADMIN_ROLE(), admin));
        assertTrue(pool.hasRole(pool.FUND_MANAGER_ROLE(), fundManager));
        assertTrue(pool.hasRole(pool.PAUSER_ROLE(), admin));
        assertTrue(pool.hasRole(pool.UPGRADER_ROLE(), admin));

        // Verify pool configuration
        assertEq(pool.withdrawalWaitPeriod(), 7 days);
        assertEq(pool.epochDuration(), 24 hours);
        // Pool is now always in BATCH_MODE

        // Verify immediate pool is initialized to 0
        assertEq(pool.totalWaitDeposits(), 0);

        // Verify contract addresses are set correctly
        assertEq(address(pool.usdt()), usdt);
        assertEq(address(pool.rpt()), rptProxy);
    }

    function testDeploymentWithCustomAddresses() public {
        // Set custom addresses via environment variables
        address customAdmin = makeAddr("customAdmin");
        address customFundManager = makeAddr("customFundManager");
        address customTellerMint = makeAddr("customTellerMint");
        address customTellerRedeem = makeAddr("customTellerRedeem");

        vm.setEnv("ADMIN_ADDRESS", vm.toString(customAdmin));
        vm.setEnv("FUND_MANAGER_ADDRESS", vm.toString(customFundManager));
        vm.setEnv("TELLER_MINT_ADDRESS", vm.toString(customTellerMint));
        vm.setEnv("TELLER_REDEEM_ADDRESS", vm.toString(customTellerRedeem));

        // Create new deploy script instance
        Deploy customDeployScript = new Deploy();
        customDeployScript.setUp();

        // Verify custom addresses are read correctly
        assertEq(customDeployScript.admin(), customAdmin);
        assertEq(customDeployScript.fundManager(), customFundManager);
        assertEq(customDeployScript.tellerMint(), customTellerMint);
        assertEq(customDeployScript.tellerRedeem(), customTellerRedeem);

        // Note: We skip the actual deployment run() in this test to avoid broadcast conflicts
        // The setUp() already validates that environment variables are read correctly
        console.log("Custom addresses test passed - environment variables read correctly");
    }

    function testMockUSDTDeployment() public {
        // Run deployment (should deploy mock USDT)
        deployScript.run();

        address usdt = deployScript.usdt();
        assertTrue(usdt != address(0), "Mock USDT not deployed");

        // Test mock USDT functionality
        MockUSDT mockUSDT = MockUSDT(usdt);
        assertEq(mockUSDT.name(), "Mock USDT");
        assertEq(mockUSDT.symbol(), "mUSDT");
        assertEq(mockUSDT.decimals(), 6);
    }

    function testBasicPoolFunctionality() public {
        // Deploy contracts
        deployScript.run();

        address poolProxy = deployScript.poolProxy();
        address rptProxy = deployScript.rptProxy();
        address usdt = deployScript.usdt();

        RetailPool pool = RetailPool(poolProxy);
        RPT rpt = RPT(rptProxy);
        MockUSDT mockUSDT = MockUSDT(usdt);

        // Test basic deposit functionality
        address user = makeAddr("user");
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT

        // Give user some USDT
        mockUSDT.mint(user, depositAmount);

        // User deposits
        vm.startPrank(user);
        mockUSDT.approve(poolProxy, depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();

        // Verify deposit worked
        assertEq(rpt.balanceOf(user), 1000 * 1e18); // 1000 RPT (1:1 initially)
        assertEq(pool.totalWaitDeposits(), depositAmount); // Should be added to immediate pool
        assertEq(pool.totalWaitDeposits(), depositAmount); // Should be tracked in batch mode
    }

    function testImmediateWithdrawFunctionality() public {
        // Deploy contracts
        deployScript.run();

        address poolProxy = deployScript.poolProxy();
        address rptProxy = deployScript.rptProxy();
        address usdt = deployScript.usdt();

        RetailPool pool = RetailPool(poolProxy);
        RPT rpt = RPT(rptProxy);
        MockUSDT mockUSDT = MockUSDT(usdt);

        // Setup users
        address alice = makeAddr("alice");
        address bob = makeAddr("bob");

        // Alice deposits 1000 USDT
        mockUSDT.mint(alice, 1000 * 1e6);

        vm.startPrank(alice);
        mockUSDT.approve(poolProxy, 1000 * 1e6);
        pool.deposit(1000 * 1e6);
        vm.stopPrank();

        // Bob gets some RPT and tries immediate withdrawal
        mockUSDT.mint(bob, 500 * 1e6);

        vm.startPrank(bob);
        mockUSDT.approve(poolProxy, 500 * 1e6);
        pool.deposit(500 * 1e6);

        // Bob should be able to withdraw immediately
        assertTrue(pool.canWithdrawImmediately(500 * 1e18));

        uint256 bobUSDTBefore = mockUSDT.balanceOf(bob);
        bool success = pool.withdrawImmediately(500 * 1e18);

        assertTrue(success);
        assertEq(mockUSDT.balanceOf(bob), bobUSDTBefore + 500 * 1e6);
        assertEq(pool.totalWaitDeposits(), 1000 * 1e6); // Alice's 1000 + Bob's 500 - Bob's 500

        vm.stopPrank();
    }

    function testAdminImmediateWithdrawFunctionality() public {
        // Deploy contracts
        deployScript.run();

        address poolProxy = deployScript.poolProxy();
        address usdt = deployScript.usdt();
        address fundManager = deployScript.fundManager();

        RetailPool pool = RetailPool(poolProxy);
        MockUSDT mockUSDT = MockUSDT(usdt);

        // Setup user deposit to create immediate pool
        address user = makeAddr("user");
        mockUSDT.mint(user, 1000 * 1e6);

        vm.startPrank(user);
        mockUSDT.approve(poolProxy, 1000 * 1e6);
        pool.deposit(1000 * 1e6);
        vm.stopPrank();

        // Fund manager should be able to withdraw immediately
        assertTrue(pool.canAdminWithdrawImmediately(500 * 1e6));

        uint256 fundManagerUSDTBefore = mockUSDT.balanceOf(fundManager);

        vm.startPrank(fundManager);
        bool success = pool.adminWithdrawImmediately(500 * 1e6);
        vm.stopPrank();

        assertTrue(success);
        assertEq(mockUSDT.balanceOf(fundManager), fundManagerUSDTBefore + 500 * 1e6);
        assertEq(pool.totalWaitDeposits(), 500 * 1e6); // 1000 - 500
    }
}
