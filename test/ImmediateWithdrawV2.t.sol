// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "./helpers/TestHelper.sol";

contract ImmediateWithdrawV2Test is TestHelper {
    function setUp() public override {
        super.setUp();

        // Set to BATCH_MODE for testing immediate withdrawals
        vm.prank(owner);
        pool.setOperationMode(RetailPool.OperationMode.BATCH_MODE);
    }

    function testWithdrawImmediatelySuccess() public {
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT
        uint256 withdrawRPT = 500 * 1e18;   // 500 RPT

        // Alice deposits 1000 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();

        // Check immediate pool has the funds
        assertEq(pool.immediatePool(), depositAmount);

        // <PERSON> gets some RPT to withdraw
        vm.startPrank(bob);
        usdt.approve(address(pool), 500 * 1e6);
        pool.deposit(500 * 1e6);

        // Bob attempts immediate withdrawal
        uint256 bobUSDTBefore = usdt.balanceOf(bob);
        bool success = pool.withdrawImmediately(withdrawRPT);

        // Verify success
        assertTrue(success);

        // Verify <PERSON> received USDT
        assertEq(usdt.balanceOf(bob), bobUSDTBefore + 500 * 1e6);

        // Verify immediate pool was reduced
        assertEq(pool.immediatePool(), depositAmount); // Alice's 1000 + Bob's 500 - Bob's withdrawal 500 = 1000

        vm.stopPrank();
    }

    function testWithdrawImmediatelyInsufficientFunds() public {
        uint256 depositAmount = 500 * 1e6; // 500 USDT
        uint256 withdrawRPT = 1200 * 1e18; // 1200 RPT (more than available immediate pool)

        // Alice deposits 500 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();

        // Bob gets RPT to withdraw (deposits 600 USDT, so total immediate pool = 1100)
        vm.startPrank(bob);
        usdt.approve(address(pool), 600 * 1e6);
        pool.deposit(600 * 1e6);

        // Bob attempts immediate withdrawal of 1200 USDT (more than 1100 in immediate pool)
        uint256 bobUSDTBefore = usdt.balanceOf(bob);
        bool success = pool.withdrawImmediately(withdrawRPT);

        // Verify failure
        assertFalse(success);

        // Verify Bob's USDT balance unchanged
        assertEq(usdt.balanceOf(bob), bobUSDTBefore);

        vm.stopPrank();
    }

    function testWithdrawImmediatelyOnlyInBatchMode() public {
        // Switch to INSTANT_MODE
        vm.prank(owner);
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);

        uint256 depositAmount = 1000 * 1e6; // 1000 USDT
        uint256 withdrawRPT = 500 * 1e18;   // 500 RPT

        // Alice deposits 1000 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);

        // Alice attempts immediate withdrawal (should fail in INSTANT_MODE)
        bool success = pool.withdrawImmediately(withdrawRPT);

        // Verify failure
        assertFalse(success);

        vm.stopPrank();
    }

    function testCanWithdrawImmediately() public {
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT

        // Alice deposits 1000 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();

        // Check query function
        assertTrue(pool.canWithdrawImmediately(500 * 1e18));  // 500 RPT - should be possible
        assertTrue(pool.canWithdrawImmediately(1000 * 1e18)); // 1000 RPT - should be possible
        assertFalse(pool.canWithdrawImmediately(1500 * 1e18)); // 1500 RPT - should not be possible

        // In INSTANT_MODE, should always return false
        // First trigger epoch to clear immediate pool, then switch mode
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        vm.prank(owner);
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);

        assertFalse(pool.canWithdrawImmediately(500 * 1e18));
    }

    function testImmediatePoolResetAfterEpoch() public {
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT

        // Alice deposits 1000 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();

        // Check immediate pool has the funds
        assertEq(pool.immediatePool(), depositAmount);

        // Fast forward time and trigger epoch
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Check immediate pool is reset to 0
        assertEq(pool.immediatePool(), 0);

        // New deposits should go to immediate pool again
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();

        assertEq(pool.immediatePool(), depositAmount);
    }

    function testImmediateWithdrawEvent() public {
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT
        uint256 withdrawRPT = 500 * 1e18;   // 500 RPT

        // Alice deposits 1000 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();

        // Bob gets RPT to withdraw
        vm.startPrank(bob);
        usdt.approve(address(pool), 500 * 1e6);
        pool.deposit(500 * 1e6);

        // Expect event emission
        vm.expectEmit(true, false, false, true);
        emit ImmediateWithdrawCompleted(bob, withdrawRPT, 500 * 1e6);

        // Bob performs immediate withdrawal
        pool.withdrawImmediately(withdrawRPT);

        vm.stopPrank();
    }

    function testScenarioFromUserQuestion() public {
        // Exact scenario from user's question:
        // User A deposits 100 USDT, User B wants to withdraw 50 USDT worth

        uint256 userADeposit = 100 * 1e6; // 100 USDT
        uint256 userBWithdrawRPT = 50 * 1e18; // 50 RPT (worth 50 USDT)

        // User A deposits 100 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), userADeposit);
        pool.deposit(userADeposit);
        vm.stopPrank();

        // Check immediate pool
        assertEq(pool.immediatePool(), userADeposit);

        // User B gets some RPT first
        vm.startPrank(bob);
        usdt.approve(address(pool), 50 * 1e6);
        pool.deposit(50 * 1e6);

        // User B can now withdraw immediately
        assertTrue(pool.canWithdrawImmediately(userBWithdrawRPT));

        uint256 bobUSDTBefore = usdt.balanceOf(bob);
        bool success = pool.withdrawImmediately(userBWithdrawRPT);

        // Verify User B got the USDT immediately
        assertTrue(success);
        assertEq(usdt.balanceOf(bob), bobUSDTBefore + 50 * 1e6);

        // No epoch trigger needed!
        // No 7-day wait period!

        vm.stopPrank();
    }

    function testMultipleImmediateWithdrawals() public {
        uint256 depositAmount = 2000 * 1e6; // 2000 USDT

        // Alice deposits 2000 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();

        // Bob withdraws 500 USDT immediately
        vm.startPrank(bob);
        usdt.approve(address(pool), 500 * 1e6);
        pool.deposit(500 * 1e6);

        bool success1 = pool.withdrawImmediately(500 * 1e18);
        assertTrue(success1);
        vm.stopPrank();

        // Charlie withdraws 800 USDT immediately
        address charlie = makeAddr("charlie");
        deal(address(usdt), charlie, 800 * 1e6);
        vm.startPrank(charlie);
        usdt.approve(address(pool), 800 * 1e6);
        pool.deposit(800 * 1e6);

        bool success2 = pool.withdrawImmediately(800 * 1e18);
        assertTrue(success2);
        vm.stopPrank();

        // Dave tries to withdraw 2500 USDT (should fail - more than immediate pool)
        // Current immediate pool: Alice's 2000 + Bob's 500 - Bob's 500 + Charlie's 800 - Charlie's 800 = 2000
        address dave = makeAddr("dave");
        deal(address(usdt), dave, 1000 * 1e6);
        vm.startPrank(dave);
        usdt.approve(address(pool), 1000 * 1e6);
        pool.deposit(1000 * 1e6); // Now immediate pool = 3000

        bool success3 = pool.withdrawImmediately(3500 * 1e18); // Try to withdraw 3500 (more than 3000)
        assertFalse(success3); // Should fail
        vm.stopPrank();
    }

    // Event declaration for testing
    event ImmediateWithdrawCompleted(address indexed user, uint256 rpt, uint256 usdt);
}
