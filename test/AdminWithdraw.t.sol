// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "./helpers/TestHelper.sol";

/**
 * @title Admin Withdrawal Test Suite
 * @notice Tests for fund manager withdrawal functionality with user protection
 */
contract AdminWithdrawTest is TestHelper {
    // Test events
    event AdminWithdrawRequested(address indexed admin, uint256 indexed requestId, uint256 usdt);
    event AdminWithdrawClaimed(address indexed admin, uint256 indexed requestId, uint256 usdt);

    function setUp() public override {
        super.setUp();
        // Set initial epoch time
        vm.warp(1000);
    }

    // ================== Basic Admin Withdrawal Tests ==================

    function testAdminWithdrawRequest() public {
        uint256 depositAmount = 10000 * 1e6; // 10k USDT
        uint256 adminWithdrawAmount = 3000 * 1e6; // 3k USDT

        // Alice deposits to provide liquidity
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Fund manager requests withdrawal
        vm.prank(fundManager);
        vm.expectEmit(true, true, false, true);
        emit AdminWithdrawRequested(fundManager, 1, adminWithdrawAmount);
        uint256 requestId = pool.adminRequestWithdraw(adminWithdrawAmount);

        assertEq(requestId, 1);

        // Check request details - now using unified withdrawRequests mapping
        (address requester, uint256 rptAmount, uint256 usdtAmount, uint256 requestTime, bool claimed, uint256 strategyRequestId, RetailPool.RequestType requestType) = pool.withdrawRequests(requestId);

        assertEq(requester, fundManager);
        assertEq(rptAmount, 0); // Admin requests don't burn RPT
        assertEq(usdtAmount, adminWithdrawAmount);
        assertEq(requestTime, block.timestamp);
        assertFalse(claimed);
        assertEq(uint256(requestType), uint256(RetailPool.RequestType.ADMIN_WITHDRAW));

        // Check admin's request list
        uint256[] memory requests = pool.getAdminWithdrawRequests(fundManager);
        assertEq(requests.length, 1);
        assertEq(requests[0], requestId);
    }

    function testAdminCannotWithdrawMoreThanAvailable() public {
        uint256 depositAmount = 10000 * 1e6; // 10k USDT
        uint256 userWithdrawAmount = 8000 * 1e6; // 8k USDT
        uint256 adminWithdrawAmount = 5000 * 1e6; // 5k USDT (more than available)

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Alice requests withdrawal - this initiates strategy redeem immediately
        vm.prank(alice);
        pool.requestWithdraw(8000 * 1e18); // Convert to RPT amount

        // Available strategy liquidity = 10k - 8k = 2k USDT
        // Fund manager tries to withdraw 5k (should fail)
        vm.prank(fundManager);
        vm.expectRevert(RetailPool.InsufficientStrategyLiquidity.selector);
        pool.adminRequestWithdraw(adminWithdrawAmount);
    }

    function testAdminWithdrawAfterWaitPeriod() public {
        uint256 depositAmount = 10000 * 1e6; // 10k USDT
        uint256 adminWithdrawAmount = 3000 * 1e6; // 3k USDT

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Fund manager requests withdrawal
        vm.prank(fundManager);
        uint256 requestId = pool.adminRequestWithdraw(adminWithdrawAmount);

        // Try to claim immediately - should fail
        vm.prank(fundManager);
        vm.expectRevert(RetailPool.WaitPeriodNotCompleted.selector);
        pool.claimWithdraw(requestId);

        // Fast forward past wait period
        vm.warp(block.timestamp + 8 days);

        uint256 fundManagerUSDTBefore = usdt.balanceOf(fundManager);

        // Claim should work now
        vm.prank(fundManager);
        vm.expectEmit(true, true, false, true);
        emit AdminWithdrawClaimed(fundManager, requestId, adminWithdrawAmount);
        pool.claimWithdraw(requestId);

        // Check results
        assertEq(usdt.balanceOf(fundManager), fundManagerUSDTBefore + adminWithdrawAmount);

        (,,,, bool claimed,,) = pool.withdrawRequests(requestId);
        assertTrue(claimed);
    }

    function testCanClaimAdminWithdrawStatus() public {
        uint256 depositAmount = 10000 * 1e6;
        uint256 adminWithdrawAmount = 3000 * 1e6;

        vm.prank(alice);
        pool.deposit(depositAmount);

        vm.prank(fundManager);
        uint256 requestId = pool.adminRequestWithdraw(adminWithdrawAmount);

        // Initially should not be claimable
        (bool canClaim, uint256 timeRemaining) = pool.canClaimWithdraw(requestId);
        assertFalse(canClaim);
        assertEq(timeRemaining, 7 days);

        // Fast forward partially
        vm.warp(block.timestamp + 3 days);
        (canClaim, timeRemaining) = pool.canClaimWithdraw(requestId);
        assertFalse(canClaim);
        assertEq(timeRemaining, 4 days);

        // After wait period
        vm.warp(block.timestamp + 5 days);
        (canClaim, timeRemaining) = pool.canClaimWithdraw(requestId);
        assertTrue(canClaim);
        assertEq(timeRemaining, 0);
    }

    // ================== User Protection Tests ==================

    function testUserFundsProtectedFromAdmin() public {
        uint256 depositAmount = 10000 * 1e6; // 10k USDT
        uint256 userWithdrawAmount = 6000 * 1e6; // 6k USDT

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Alice requests withdrawal
        vm.prank(alice);
        uint256 userRequestId = pool.requestWithdraw(6000 * 1e18);

        // Available for admin should be 10k - 6k = 4k USDT
        uint256 availableForAdmin = 4000 * 1e6;

        // Fund manager can withdraw up to available amount
        vm.prank(fundManager);
        uint256 adminRequestId = pool.adminRequestWithdraw(availableForAdmin);

        // Fund manager tries to withdraw more - should fail
        vm.prank(fundManager);
        vm.expectRevert(RetailPool.InsufficientLiquidity.selector);
        pool.adminRequestWithdraw(1 * 1e6); // Even 1 USDT more should fail

        // Fast forward and both should be able to claim
        vm.warp(block.timestamp + 8 days);

        // Admin claims first
        vm.prank(fundManager);
        pool.claimWithdraw(adminRequestId);

        // User should still be able to claim (their funds were protected)
        vm.prank(alice);
        pool.claimWithdraw(userRequestId);

        // Verify final balances
        assertEq(usdt.balanceOf(address(pool)), 0); // All funds distributed
    }

    function testLiquidityCalculationWithReservedFunds() public {
        uint256 depositAmount = 10000 * 1e6; // 10k USDT

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Initial liquidity should equal deposit
        assertEq(pool.liquidity(), depositAmount);

        // Alice requests withdrawal of 6k USDT worth
        vm.prank(alice);
        pool.requestWithdraw(6000 * 1e18);

        // Available liquidity should now be 10k - 6k (user reserved) = 4k
        // After Alice's withdrawal request, 6k USDT is reserved for her claim
        assertEq(pool.liquidity(), 4000 * 1e6); // 4k USDT available after user reserved funds
    }

    function testMultipleAdminWithdrawRequests() public {
        uint256 depositAmount = 20000 * 1e6; // 20k USDT

        vm.prank(alice);
        pool.deposit(depositAmount);

        // Fund manager creates multiple requests
        vm.prank(fundManager);
        uint256 requestId1 = pool.adminRequestWithdraw(5000 * 1e6);

        vm.prank(fundManager);
        uint256 requestId2 = pool.adminRequestWithdraw(3000 * 1e6);

        // Check admin's requests
        uint256[] memory requests = pool.getAdminWithdrawRequests(fundManager);
        assertEq(requests.length, 2);
        assertEq(requests[0], requestId1);
        assertEq(requests[1], requestId2);

        // Fast forward and claim both
        vm.warp(block.timestamp + 8 days);

        vm.prank(fundManager);
        pool.claimWithdraw(requestId1);

        vm.prank(fundManager);
        pool.claimWithdraw(requestId2);

        assertEq(usdt.balanceOf(fundManager), 1_000_000 * 1e6 + 8000 * 1e6);
    }

    // ================== Access Control Tests ==================

    function testOnlyFundManagerCanWithdraw() public {
        uint256 depositAmount = 10000 * 1e6;

        vm.prank(alice);
        pool.deposit(depositAmount);

        // Alice tries to make admin withdrawal - should fail
        vm.prank(alice);
        vm.expectRevert();
        pool.adminRequestWithdraw(1000 * 1e6);

        // Bob tries to make admin withdrawal - should fail
        vm.prank(bob);
        vm.expectRevert();
        pool.adminRequestWithdraw(1000 * 1e6);

        // Only fund manager should work
        vm.prank(fundManager);
        uint256 requestId = pool.adminRequestWithdraw(1000 * 1e6);

        // Others can't claim fund manager's request
        vm.warp(block.timestamp + 8 days);

        vm.prank(alice);
        vm.expectRevert();
        pool.claimWithdraw(requestId);
    }

    function testAdminCannotClaimTwice() public {
        uint256 depositAmount = 10000 * 1e6;

        vm.prank(alice);
        pool.deposit(depositAmount);

        vm.prank(fundManager);
        uint256 requestId = pool.adminRequestWithdraw(1000 * 1e6);

        vm.warp(block.timestamp + 8 days);

        vm.prank(fundManager);
        pool.claimWithdraw(requestId);

        // Try to claim again - should fail
        vm.prank(fundManager);
        vm.expectRevert(RetailPool.RequestAlreadyClaimed.selector);
        pool.claimWithdraw(requestId);
    }

    // ================== Edge Cases Tests ==================

    function testZeroAmountAdminWithdraw() public {
        vm.prank(fundManager);
        vm.expectRevert(RetailPool.InvalidAmount.selector);
        pool.adminRequestWithdraw(0);
    }

    function testInvalidAdminRequestId() public {
        vm.prank(fundManager);
        vm.expectRevert(RetailPool.InvalidRequestId.selector);
        pool.claimWithdraw(999); // Non-existent request ID
    }

    function testAdminWithdrawWithNoLiquidity() public {
        // No deposits, fund manager tries to withdraw
        vm.prank(fundManager);
        vm.expectRevert(RetailPool.InsufficientLiquidity.selector);
        pool.adminRequestWithdraw(1000 * 1e6);
    }

    function testLiquidityChangesAfterUserWithdraw() public {
        uint256 depositAmount = 10000 * 1e6;

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Alice requests withdrawal
        vm.prank(alice);
        uint256 userRequestId = pool.requestWithdraw(6000 * 1e18);

        // Fast forward and Alice claims
        vm.warp(block.timestamp + 8 days);
        vm.prank(alice);
        pool.claimWithdraw(userRequestId);

        // Now fund manager should be able to withdraw from remaining balance
        vm.prank(fundManager);
        uint256 adminRequestId = pool.adminRequestWithdraw(4000 * 1e6);

        vm.warp(block.timestamp + 8 days);
        vm.prank(fundManager);
        pool.claimWithdraw(adminRequestId);
    }

    // ================== Integration with User Withdrawals ==================

    function testUserAndAdminWithdrawRace() public {
        uint256 depositAmount = 10000 * 1e6;

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Both user and admin request withdrawals at same time
        vm.prank(alice);
        uint256 userRequestId = pool.requestWithdraw(6000 * 1e18);

        vm.prank(fundManager);
        uint256 adminRequestId = pool.adminRequestWithdraw(4000 * 1e6);

        // Fast forward
        vm.warp(block.timestamp + 8 days);

        // Both should be able to claim in any order
        vm.prank(fundManager);
        pool.claimWithdraw(adminRequestId);

        vm.prank(alice);
        pool.claimWithdraw(userRequestId);

        // All funds should be distributed
        assertEq(usdt.balanceOf(address(pool)), 0);
    }

    function testUserWithdrawFailsAfterAdminDrainsLiquidity() public {
        uint256 depositAmount = 10000 * 1e6;

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Fund manager withdraws most funds first
        vm.prank(fundManager);
        uint256 adminRequestId = pool.adminRequestWithdraw(9000 * 1e6);

        vm.warp(block.timestamp + 8 days);
        vm.prank(fundManager);
        pool.claimWithdraw(adminRequestId);

        // Check available liquidity - should be 1k USDT
        uint256 availableLiquidity = pool.liquidity();
        assertEq(availableLiquidity, 1000 * 1e6);

        // Alice requests withdrawal of all her RPT (worth exactly 1k USDT)
        vm.prank(alice);
        uint256 allRPT = 10000 * 1e18;
        pool.requestWithdraw(allRPT); // This should succeed

        // Check available liquidity after Alice's request - should be 0
        availableLiquidity = pool.liquidity();
        assertEq(availableLiquidity, 0);

        // Now Bob deposits some funds - this should increase total balance
        vm.prank(bob);
        pool.deposit(500 * 1e6); // Bob deposits 500 USDT

        // But available liquidity should still be very limited due to Alice's reserved funds
        // Bob tries to withdraw more than what's available after reserving Alice's funds
        vm.prank(bob);
        vm.expectRevert(RetailPool.InsufficientLiquidity.selector);
        pool.requestWithdraw(400 * 1e18); // Try to withdraw RPT worth more than available liquidity
    }
}
