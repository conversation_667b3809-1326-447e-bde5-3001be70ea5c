// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import {console} from "forge-std/console.sol";
import "./helpers/IntegrationTestHelper.sol";

/**
 * @title RetailPool Integration Test Suite
 * @notice Integration tests using real Teller contracts from environment variables
 * @dev Requires TellerMinterAddr and TellerRedeemAddr to be set in environment
 */
contract RetailPoolIntegrationTest is IntegrationTestHelper {
    // Test events
    event WithdrawRequested(address indexed user, uint256 indexed requestId, uint256 rpt, uint256 usdt);
    event WithdrawClaimed(address indexed user, uint256 indexed requestId, uint256 rpt, uint256 usdt);
    event RPTStaked(address indexed user, uint256 amount, uint256 lockDuration);
    event EpochTriggered(address indexed caller, uint256 timestamp, int256 netAmount);

    function setUp() public override {
        super.setUp();
        fundTestAccounts();
    }

    // ================== Basic Functionality Tests ==================

    function testInitialStateIntegration() public view requireIntegrationSetup {
        assertEq(address(pool.usdt()), address(usdt));
        assertEq(address(pool.rpt()), address(rpt));
        assertEq(address(pool.mintStrategy()), tellerMinterAddr);
        assertEq(address(pool.redeemStrategy()), tellerRedeemAddr);

        // Check initial values
        assertEq(pool.totalWaitDeposits(), 0);
        assertEq(pool.userWaitWithdrawals(), 0);
        assertEq(pool.strategyShares(), 0);
        assertEq(pool.withdrawalWaitPeriod(), 7 days);
        assertEq(pool.epochDuration(), 24 hours);

        // Initial RPT price should be 1:1
        assertEq(pool.rptPrice(), 1e18);
    }

    function testSimpleDepositIntegration() public requireIntegrationSetup {
        uint256 depositAmount = 1000 * 1e6;

        // Skip if using real USDT without sufficient balance
        if (usdtAddr != address(0) && usdt.balanceOf(alice) < depositAmount) {
            console.log("Skipping: Insufficient real USDT balance for alice");
            return;
        }

        vm.startPrank(alice);

        // Approve if needed
        if (usdt.allowance(alice, address(pool)) < depositAmount) {
            usdt.approve(address(pool), type(uint256).max);
        }

        uint256 usdtBefore = usdt.balanceOf(alice);
        uint256 rptBefore = rpt.balanceOf(alice);

        pool.deposit(depositAmount);

        uint256 usdtAfter = usdt.balanceOf(alice);
        uint256 rptAfter = rpt.balanceOf(alice);

        vm.stopPrank();

        // Check results
        assertEq(usdtAfter, usdtBefore - depositAmount);
        assertEq(rptAfter, rptBefore + depositAmount * 1e12); // Convert from 6 to 18 decimals
        assertEq(pool.totalWaitDeposits(), depositAmount);
    }

    function testWithdrawRequestIntegration() public requireIntegrationSetup {
        uint256 depositAmount = 1000 * 1e6;

        // Skip if using real USDT without sufficient balance
        if (usdtAddr != address(0) && usdt.balanceOf(alice) < depositAmount) {
            console.log("Skipping: Insufficient real USDT balance for alice");
            return;
        }

        vm.startPrank(alice);

        // Approve and deposit
        if (usdt.allowance(alice, address(pool)) < depositAmount) {
            usdt.approve(address(pool), type(uint256).max);
        }
        pool.deposit(depositAmount);

        uint256 withdrawRPT = 500 * 1e18;

        vm.expectEmit(true, true, false, true);
        emit WithdrawRequested(alice, 1, withdrawRPT, 500 * 1e6);

        uint256 requestId = pool.requestWithdraw(withdrawRPT);
        vm.stopPrank();

        // Check request was created
        assertEq(requestId, 1);
        assertEq(pool.userWaitWithdrawals(), 500 * 1e6);

        // Check request details
        (address requester, uint256 rptAmount, uint256 usdtAmount, uint256 requestTime, bool claimed,,) =
            pool.withdrawRequests(requestId);

        assertEq(requester, alice);
        assertEq(rptAmount, withdrawRPT);
        assertEq(usdtAmount, 500 * 1e6);
        assertEq(requestTime, block.timestamp);
        assertFalse(claimed);
    }

    // ================== Epoch and Strategy Tests ==================

    function testTriggerEpochWithDepositsIntegration() public requireIntegrationSetup {
        uint256 depositAmount = 1000 * 1e6;

        // Skip if using real USDT without sufficient balance
        if (usdtAddr != address(0) && usdt.balanceOf(alice) < depositAmount) {
            console.log("Skipping: Insufficient real USDT balance for alice");
            return;
        }

        // Alice deposits
        vm.startPrank(alice);
        if (usdt.allowance(alice, address(pool)) < depositAmount) {
            usdt.approve(address(pool), type(uint256).max);
        }
        pool.deposit(depositAmount);
        vm.stopPrank();

        // Wait for epoch duration
        vm.warp(block.timestamp + 25 hours);

        uint256 sharesBefore = pool.strategyShares();
        console.log("Strategy shares before epoch:", sharesBefore);

        // Check strategy balances before
        (uint256 mintAssetsBefore,) = checkStrategyBalances();
        console.log("Mint strategy assets before:", mintAssetsBefore);

        // Trigger epoch
        vm.expectEmit(true, false, false, false);
        emit EpochTriggered(address(this), block.timestamp, int256(depositAmount));

        pool.triggerEpoch();

        uint256 sharesAfter = pool.strategyShares();
        console.log("Strategy shares after epoch:", sharesAfter);

        // Check strategy balances after
        (uint256 mintAssetsAfter, uint256 redeemValue) = checkStrategyBalances();
        console.log("Mint strategy assets after:", mintAssetsAfter);
        console.log("Redeem strategy value:", redeemValue);

        // With real contracts, we expect some shares to be created
        assertGt(sharesAfter, sharesBefore);
        assertEq(pool.totalWaitDeposits(), 0); // Should be reset after epoch
    }

    function testRPTPriceWithRealStrategyIntegration() public requireIntegrationSetup {
        uint256 depositAmount = 1000 * 1e6;

        // Skip if using real USDT without sufficient balance
        if (usdtAddr != address(0) && usdt.balanceOf(alice) < depositAmount) {
            console.log("Skipping: Insufficient real USDT balance for alice");
            return;
        }

        vm.startPrank(alice);
        if (usdt.allowance(alice, address(pool)) < depositAmount) {
            usdt.approve(address(pool), type(uint256).max);
        }
        pool.deposit(depositAmount);
        vm.stopPrank();

        // Initial price should be 1:1
        assertEq(pool.rptPrice(), 1e18);

        // Fast forward and trigger epoch
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // After epoch, price calculation includes strategy value
        uint256 newPrice = pool.rptPrice();
        console.log("RPT price after epoch:", newPrice);

        // Price should still be reasonable (not zero or extremely high)
        assertGt(newPrice, 0.5e18); // At least 0.5 USDT per RPT
        assertLt(newPrice, 2e18);   // At most 2 USDT per RPT (allowing for some strategy gains)
    }

    // ================== Liquidity Tests ==================

    function testLiquidityCalculationIntegration() public requireIntegrationSetup {
        uint256 depositAmount = 1000 * 1e6;

        // Skip if using real USDT without sufficient balance
        if (usdtAddr != address(0) && usdt.balanceOf(alice) < depositAmount) {
            console.log("Skipping: Insufficient real USDT balance for alice");
            return;
        }

        vm.startPrank(alice);
        if (usdt.allowance(alice, address(pool)) < depositAmount) {
            usdt.approve(address(pool), type(uint256).max);
        }
        pool.deposit(depositAmount);
        vm.stopPrank();

        // Liquidity should equal USDT balance in pool
        uint256 liquidity = pool.liquidity();
        uint256 poolBalance = usdt.balanceOf(address(pool));
        console.log("Pool liquidity:", liquidity);
        console.log("Pool USDT balance:", poolBalance);

        assertEq(liquidity, poolBalance);
    }

    // ================== Admin Function Tests ==================

    function testFundInjectionIntegration() public requireIntegrationSetup {
        uint256 injectionAmount = 1000 * 1e6;

        // Skip if using real USDT without sufficient balance
        if (usdtAddr != address(0) && usdt.balanceOf(fundManager) < injectionAmount) {
            console.log("Skipping: Insufficient real USDT balance for fund manager");
            return;
        }

        vm.startPrank(fundManager);
        if (usdt.allowance(fundManager, address(pool)) < injectionAmount) {
            usdt.approve(address(pool), type(uint256).max);
        }

        uint256 balanceBefore = usdt.balanceOf(address(pool));
        pool.injectFunds(injectionAmount);
        uint256 balanceAfter = usdt.balanceOf(address(pool));

        vm.stopPrank();

        assertEq(balanceAfter, balanceBefore + injectionAmount);
        assertEq(pool.totalWaitDeposits(), injectionAmount);
    }

    function testAdminWithdrawIntegration() public requireIntegrationSetup {
        uint256 injectionAmount = 1000 * 1e6;
        uint256 withdrawAmount = 500 * 1e6;

        // Skip if using real USDT without sufficient balance
        if (usdtAddr != address(0) && usdt.balanceOf(fundManager) < injectionAmount) {
            console.log("Skipping: Insufficient real USDT balance for fund manager");
            return;
        }

        // First inject some funds
        vm.startPrank(fundManager);
        if (usdt.allowance(fundManager, address(pool)) < injectionAmount) {
            usdt.approve(address(pool), type(uint256).max);
        }
        pool.injectFunds(injectionAmount);

        // Request admin withdrawal
        uint256 requestId = pool.adminRequestWithdraw(withdrawAmount);
        assertEq(requestId, 1);

        // Fast forward past wait period
        vm.warp(block.timestamp + 8 days);

        uint256 balanceBefore = usdt.balanceOf(fundManager);
        pool.claimWithdraw(requestId);
        uint256 balanceAfter = usdt.balanceOf(fundManager);

        vm.stopPrank();

        assertEq(balanceAfter, balanceBefore + withdrawAmount);
    }

    // ================== Access Control Tests ==================

    function testRoleBasedAccessIntegration() public view requireIntegrationSetup {
        assertTrue(pool.hasRole(pool.DEFAULT_ADMIN_ROLE(), owner));
        assertTrue(pool.hasRole(pool.FUND_MANAGER_ROLE(), fundManager));
        assertTrue(pool.hasRole(pool.STRATEGY_ROLE(), tellerMinterAddr));
        assertTrue(pool.hasRole(pool.STRATEGY_ROLE(), tellerRedeemAddr));
        assertTrue(pool.hasRole(pool.PAUSER_ROLE(), owner));
        assertTrue(pool.hasRole(pool.UPGRADER_ROLE(), owner));
    }

    // ================== Integration-Specific Tests ==================

    function testRealStrategyInteractionIntegration() public requireIntegrationSetup {
        uint256 depositAmount = 100 * 1e6; // Smaller amount for real contracts

        // Skip if using real USDT without sufficient balance
        if (usdtAddr != address(0) && usdt.balanceOf(alice) < depositAmount) {
            console.log("Skipping: Insufficient real USDT balance for alice");
            return;
        }

        vm.startPrank(alice);
        if (usdt.allowance(alice, address(pool)) < depositAmount) {
            usdt.approve(address(pool), type(uint256).max);
        }
        pool.deposit(depositAmount);
        vm.stopPrank();

        // Trigger epoch to interact with real strategies
        vm.warp(block.timestamp + 25 hours);

        console.log("About to trigger epoch with real strategies...");
        console.log("Pool USDT balance:", usdt.balanceOf(address(pool)));
        console.log("Total wait deposits:", pool.totalWaitDeposits());

        // This will actually call the real Teller contracts
        pool.triggerEpoch();

        console.log("Epoch triggered successfully");
        console.log("Strategy shares:", pool.strategyShares());

        // Check that strategy interaction occurred
        uint256 shares = pool.strategyShares();
        if (shares > 0) {
            console.log("Successfully interacted with real strategy contracts");

            // Try to get strategy value
            try redeemStrategy.previewRedeem(shares) returns (uint256 value) {
                console.log("Strategy value:", value);
                assertGt(value, 0);
            } catch {
                console.log("Could not preview redeem - might be normal for some strategies");
            }
        }
    }

    function testCooldownPeriodIntegration() public view requireIntegrationSetup {
        // Test the real strategy's cooldown period
        try redeemStrategy.cooldownPeriod() returns (uint256 period) {
            console.log("Real strategy cooldown period:", period);
            assertGt(period, 0);
        } catch {
            console.log("Real strategy does not expose cooldown period");
        }
    }

    // ================== Helper Functions ==================

    function testEnvironmentSetup() public {
        console.log("=== Integration Test Environment ===");
        console.log("Teller Mint Strategy:", tellerMinterAddr);
        console.log("Teller Redeem Strategy:", tellerRedeemAddr);
        console.log("USDT Address:", usdtAddr);
        console.log("Using Mock USDT:", usdtAddr == address(0));

        if (tellerMinterAddr == address(0) || tellerRedeemAddr == address(0)) {
            console.log("WARNING: Integration tests will be skipped");
            console.log("Set TellerMinterAddr and TellerRedeemAddr environment variables");
        } else {
            console.log("Integration test environment ready");
        }
    }
}
