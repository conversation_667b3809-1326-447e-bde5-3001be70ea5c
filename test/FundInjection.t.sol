// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "./helpers/TestHelper.sol";
import "./mocks/MockTellerMint.sol";

contract FundInjectionTest is TestHelper {
    function setUp() public override {
        super.setUp();
    }

    // ================== Fund Injection Tests ==================

    function testFundManagerCanInjectFunds() public {
        uint256 injectionAmount = 5000 * 1e6; // 5k USDT

        // Fund manager injects funds
        vm.prank(fundManager);
        usdt.approve(address(pool), injectionAmount);

        vm.prank(fundManager);
        pool.injectFunds(injectionAmount);

        // In batch mode (default), funds wait for epoch
        assertEq(pool.totalWaitDeposits(), injectionAmount);
        assertEq(pool.strategyShares(), 0); // No immediate staking in batch mode
        assertEq(usdt.balanceOf(address(pool)), injectionAmount); // USDT stays in contract
    }

    function testOnlyFundManagerCanInjectFunds() public {
        uint256 injectionAmount = 1000 * 1e6;

        // Alice tries to inject funds - should fail
        vm.prank(alice);
        usdt.approve(address(pool), injectionAmount);

        vm.prank(alice);
        vm.expectRevert();
        pool.injectFunds(injectionAmount);

        // Owner tries to inject funds - should fail
        vm.prank(owner);
        usdt.approve(address(pool), injectionAmount);

        vm.prank(owner);
        vm.expectRevert();
        pool.injectFunds(injectionAmount);
    }

    function testCannotInjectZeroAmount() public {
        vm.prank(fundManager);
        vm.expectRevert(RetailPool.InvalidAmount.selector);
        pool.injectFunds(0);
    }

    function testInjectFundsEmitsEvent() public {
        uint256 injectionAmount = 2000 * 1e6;

        vm.prank(fundManager);
        usdt.approve(address(pool), injectionAmount);

        vm.prank(fundManager);
        vm.expectEmit(true, false, false, true);
        emit RetailPool.FundsInjected(fundManager, injectionAmount);
        pool.injectFunds(injectionAmount);
    }

    function testMultipleFundInjections() public {
        uint256 injection1 = 1000 * 1e6;
        uint256 injection2 = 2000 * 1e6;

        // First injection
        vm.prank(fundManager);
        usdt.approve(address(pool), injection1);
        vm.prank(fundManager);
        pool.injectFunds(injection1);

        // Second injection
        vm.prank(fundManager);
        usdt.approve(address(pool), injection2);
        vm.prank(fundManager);
        pool.injectFunds(injection2);

        // Total should be cumulative
        assertEq(pool.totalWaitDeposits(), injection1 + injection2);
        assertEq(usdt.balanceOf(address(pool)), injection1 + injection2);
    }

    // ================== Integration with triggerEpoch ==================

    function testInjectedFundsIncludedInEpochCalculation() public {
        uint256 injectionAmount = 3000 * 1e6;

        // Fund manager injects funds
        vm.prank(fundManager);
        usdt.approve(address(pool), injectionAmount);
        vm.prank(fundManager);
        pool.injectFunds(injectionAmount);

        // Check injection recorded
        assertEq(pool.totalWaitDeposits(), injectionAmount);

        // Trigger epoch - should deposit injected funds to strategy
        vm.warp(block.timestamp + 25 hours); // Move past epoch duration
        pool.triggerEpoch();

        // Injected funds should be reset after epoch
        assertEq(pool.totalWaitDeposits(), 0);

        // Strategy should have received the funds
        assertEq(MockTellerMint(address(mintStrategy)).totalAssets(), injectionAmount);
    }

    function testInjectedFundsWithUserDepositsInEpoch() public {
        uint256 userDeposit = 5000 * 1e6;
        uint256 injectionAmount = 2000 * 1e6;

        // User deposits
        vm.prank(alice);
        pool.deposit(userDeposit);

        // Fund manager injects additional funds
        vm.prank(fundManager);
        usdt.approve(address(pool), injectionAmount);
        vm.prank(fundManager);
        pool.injectFunds(injectionAmount);

        // Trigger epoch
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Strategy should receive both user deposit and injected funds
        assertEq(MockTellerMint(address(mintStrategy)).totalAssets(), userDeposit + injectionAmount);
        assertEq(pool.totalWaitDeposits(), 0); // Reset after epoch
    }

    function testInjectedFundsWithPendingWithdrawals() public {
        uint256 userDeposit = 10000 * 1e6;
        uint256 userWithdraw = 3000 * 1e6;
        uint256 injectionAmount = 2000 * 1e6;

        // User deposits
        vm.prank(alice);
        pool.deposit(userDeposit);

        // User requests withdrawal
        vm.prank(alice);
        uint256 rptToWithdraw = (userWithdraw * 1e18) / 1e6; // 1:1 ratio initially
        pool.requestWithdraw(rptToWithdraw);

        // Fund manager injects funds
        vm.prank(fundManager);
        usdt.approve(address(pool), injectionAmount);
        vm.prank(fundManager);
        pool.injectFunds(injectionAmount);

        // Trigger epoch
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Net amount should be: userDeposit + injection - userWithdraw = 10k + 2k - 3k = 9k
        assertEq(MockTellerMint(address(mintStrategy)).totalAssets(), 9000 * 1e6);
        assertEq(pool.totalWaitDeposits(), 0);
    }

    function testInjectedFundsWithAdminWithdrawals() public {
        uint256 userDeposit = 15000 * 1e6;
        uint256 adminWithdraw = 5000 * 1e6;
        uint256 injectionAmount = 3000 * 1e6;

        // User deposits
        vm.prank(alice);
        pool.deposit(userDeposit);

        // Admin requests withdrawal
        vm.prank(fundManager);
        pool.adminRequestWithdraw(adminWithdraw);

        // Fund manager injects funds
        vm.prank(fundManager);
        usdt.approve(address(pool), injectionAmount);
        vm.prank(fundManager);
        pool.injectFunds(injectionAmount);

        // Trigger epoch
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Net amount should be: userDeposit + injection - adminWithdraw = 15k + 3k - 5k = 13k
        assertEq(MockTellerMint(address(mintStrategy)).totalAssets(), 13000 * 1e6);
        assertEq(pool.totalWaitDeposits(), 0);
    }

    function testComplexScenarioWithAllFundTypes() public {
        uint256 userDeposit = 20000 * 1e6;
        uint256 userWithdraw = 4000 * 1e6;
        uint256 adminWithdraw = 6000 * 1e6;
        uint256 injectionAmount = 5000 * 1e6;

        // User deposits
        vm.prank(alice);
        pool.deposit(userDeposit);

        // User requests withdrawal
        vm.prank(alice);
        uint256 rptToWithdraw = (userWithdraw * 1e18) / 1e6;
        pool.requestWithdraw(rptToWithdraw);

        // Admin requests withdrawal
        vm.prank(fundManager);
        pool.adminRequestWithdraw(adminWithdraw);

        // Fund manager injects funds
        vm.prank(fundManager);
        usdt.approve(address(pool), injectionAmount);
        vm.prank(fundManager);
        pool.injectFunds(injectionAmount);

        // Trigger epoch
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Net amount: 20k + 5k - 4k - 6k = 15k
        assertEq(MockTellerMint(address(mintStrategy)).totalAssets(), 15000 * 1e6);
        assertEq(pool.totalWaitDeposits(), 0);
    }
}
