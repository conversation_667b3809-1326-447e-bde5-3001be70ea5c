// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "./helpers/TestHelper.sol";

/**
 * @title Dual Mode Test Suite
 * @notice Tests for dual operation mode functionality (BATCH_MODE vs INSTANT_MODE)
 */
contract DualModeTest is TestHelper {
    // Test events
    event OperationModeChanged(RetailPool.OperationMode oldMode, RetailPool.OperationMode newMode, address indexed changedBy);
    event BatchOperationsFlush(uint256 deposits, uint256 injections, uint256 userWithdrawals, uint256 adminWithdrawals);

    function setUp() public override {
        super.setUp();
        // Set initial epoch time
        vm.warp(1000);
    }

    // ================== Mode Switching Tests ==================

    function testInitialModeIsBatch() public view {
        assertEq(uint256(pool.operationMode()), uint256(RetailPool.OperationMode.BATCH_MODE));
    }

    function testAdminCanSwitchMode() public {
        // Switch to instant mode
        vm.prank(owner);
        vm.expectEmit(true, true, true, true);
        emit OperationModeChanged(RetailPool.OperationMode.BATCH_MODE, RetailPool.OperationMode.INSTANT_MODE, owner);
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);

        assertEq(uint256(pool.operationMode()), uint256(RetailPool.OperationMode.INSTANT_MODE));
    }

    function testNonAdminCannotSwitchMode() public {
        vm.prank(alice);
        vm.expectRevert();
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);
    }

    function testCannotSwitchModeWithPendingBatchOperations() public {
        // Create pending batch operations
        vm.prank(alice);
        pool.deposit(1000 * 1e6);

        // Try to switch mode - should fail
        vm.prank(owner);
        vm.expectRevert(RetailPool.CannotSwitchModeWithPendingOperations.selector);
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);
    }

    function testCanSwitchModeStatus() public view {
        // Initially should be able to switch (no pending operations)
        assertTrue(pool.canSwitchMode());
    }

    function testCannotSwitchModeStatusWithPendingOperations() public {
        // Create pending batch operations
        vm.prank(alice);
        pool.deposit(1000 * 1e6);

        // Should not be able to switch
        assertFalse(pool.canSwitchMode());
    }

    // ================== Batch Mode Tests ==================

    function testDepositInBatchMode() public {
        uint256 depositAmount = 1000 * 1e6;

        vm.prank(alice);
        pool.deposit(depositAmount);

        // In batch mode, funds should wait for epoch
        assertEq(pool.totalWaitDeposits(), depositAmount);
        assertEq(pool.strategyShares(), 0); // No immediate staking
        assertEq(usdt.balanceOf(address(pool)), depositAmount); // USDT stays in contract
    }

    function testInjectFundsInBatchMode() public {
        uint256 injectionAmount = 2000 * 1e6;

        vm.prank(fundManager);
        usdt.approve(address(pool), injectionAmount);
        vm.prank(fundManager);
        pool.injectFunds(injectionAmount);

        // In batch mode, funds should wait for epoch
        assertEq(pool.totalInjectedFunds(), injectionAmount);
        assertEq(pool.strategyShares(), 0); // No immediate staking
    }

    function testWithdrawRequestInBatchMode() public {
        uint256 depositAmount = 1000 * 1e6;
        uint256 withdrawRPT = 500 * 1e18;

        // Alice deposits first
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Alice requests withdrawal
        vm.prank(alice);
        uint256 requestId = pool.requestWithdraw(withdrawRPT);

        // In batch mode, should track wait withdrawals
        assertEq(pool.userWaitWithdrawals(), 500 * 1e6);

        // Check request details - no strategy request in batch mode
        (,,,, bool claimed, uint256 strategyRequestId,) = pool.withdrawRequests(requestId);
        assertFalse(claimed);
        assertEq(strategyRequestId, 0); // No strategy request in batch mode
    }

    function testTriggerEpochInBatchMode() public {
        uint256 depositAmount = 1000 * 1e6;

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Fast forward to allow epoch trigger
        vm.warp(block.timestamp + 25 hours);

        // Trigger epoch
        pool.triggerEpoch();

        // Should have processed deposits
        assertEq(pool.totalWaitDeposits(), 0);
        assertEq(pool.strategyShares(), depositAmount); // Funds staked
        assertEq(usdt.balanceOf(address(pool)), 0); // USDT moved to strategy
    }

    // ================== Instant Mode Tests ==================

    function testDepositInInstantMode() public {
        // Switch to instant mode
        vm.prank(owner);
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);

        uint256 depositAmount = 1000 * 1e6;

        vm.prank(alice);
        pool.deposit(depositAmount);

        // In instant mode, funds should be immediately staked
        assertEq(pool.totalWaitDeposits(), 0); // No waiting
        assertEq(pool.strategyShares(), depositAmount); // Immediate staking
        assertEq(usdt.balanceOf(address(pool)), 0); // USDT moved to strategy
    }

    function testInjectFundsInInstantMode() public {
        // Switch to instant mode
        vm.prank(owner);
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);

        uint256 injectionAmount = 2000 * 1e6;

        vm.prank(fundManager);
        usdt.approve(address(pool), injectionAmount);
        vm.prank(fundManager);
        pool.injectFunds(injectionAmount);

        // In instant mode, funds should be immediately staked
        assertEq(pool.totalInjectedFunds(), 0); // No waiting
        assertEq(pool.strategyShares(), injectionAmount); // Immediate staking
    }

    function testWithdrawRequestInInstantMode() public {
        // Switch to instant mode and setup
        vm.prank(owner);
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);

        uint256 depositAmount = 1000 * 1e6;
        uint256 withdrawRPT = 500 * 1e18;

        // Alice deposits first
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Sync mock redeem strategy with mint strategy state
        mockRedeemStrategy.setTotalShares(mockMintStrategy.totalShares());
        mockRedeemStrategy.setTotalAssets(mockMintStrategy.totalAssets());

        // Alice requests withdrawal
        vm.prank(alice);
        uint256 requestId = pool.requestWithdraw(withdrawRPT);

        // In instant mode, should not track wait withdrawals
        assertEq(pool.userWaitWithdrawals(), 0);

        // Should have initiated strategy redeem
        assertEq(pool.strategyShares(), 500 * 1e6); // Half redeemed

        // Check request details - should have strategy request
        (,,,, bool claimed, uint256 strategyRequestId,) = pool.withdrawRequests(requestId);
        assertFalse(claimed);
        assertGt(strategyRequestId, 0); // Should have strategy request
    }

    // ================== Mode Switching with Flush ==================

    function testFlushBatchOperations() public {
        uint256 depositAmount = 1000 * 1e6;
        uint256 injectionAmount = 500 * 1e6;

        // Create pending batch operations
        vm.prank(alice);
        pool.deposit(depositAmount);

        vm.prank(fundManager);
        usdt.approve(address(pool), injectionAmount);
        vm.prank(fundManager);
        pool.injectFunds(injectionAmount);

        // Flush operations
        vm.prank(owner);
        vm.expectEmit(true, true, true, true);
        emit BatchOperationsFlush(depositAmount, injectionAmount, 0, 0);
        pool.flushBatchOperations();

        // Should have processed all operations
        assertEq(pool.totalWaitDeposits(), 0);
        assertEq(pool.totalInjectedFunds(), 0);
        assertEq(pool.strategyShares(), depositAmount + injectionAmount);
    }

    function testSwitchModeAfterFlush() public {
        uint256 depositAmount = 1000 * 1e6;

        // Create pending batch operations
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Flush operations
        vm.prank(owner);
        pool.flushBatchOperations();

        // Now should be able to switch mode
        assertTrue(pool.canSwitchMode());

        vm.prank(owner);
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);

        assertEq(uint256(pool.operationMode()), uint256(RetailPool.OperationMode.INSTANT_MODE));
    }

    function testCannotFlushInInstantMode() public {
        // Switch to instant mode
        vm.prank(owner);
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);

        // Try to flush - should fail
        vm.prank(owner);
        vm.expectRevert(RetailPool.InvalidOperationMode.selector);
        pool.flushBatchOperations();
    }

    // ================== Liquidity Calculation Tests ==================

    function testLiquidityCalculationInBatchMode() public {
        uint256 depositAmount = 1000 * 1e6;

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Alice requests withdrawal
        vm.prank(alice);
        pool.requestWithdraw(500 * 1e18);

        // In batch mode, liquidity should account for wait withdrawals
        uint256 expectedLiquidity = depositAmount - 500 * 1e6; // Deposit minus pending withdrawal
        assertEq(pool.liquidity(), expectedLiquidity);
    }

    function testLiquidityCalculationInInstantMode() public {
        // Switch to instant mode
        vm.prank(owner);
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);

        uint256 depositAmount = 1000 * 1e6;

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Sync mock redeem strategy with mint strategy state
        mockRedeemStrategy.setTotalShares(mockMintStrategy.totalShares());
        mockRedeemStrategy.setTotalAssets(mockMintStrategy.totalAssets());

        // Alice requests withdrawal
        vm.prank(alice);
        pool.requestWithdraw(500 * 1e18);

        // In instant mode, liquidity should account for pending redemptions
        // Strategy shares reduced, pending redemption amount tracked
        uint256 expectedLiquidity = 500 * 1e6; // Remaining strategy value minus pending redemptions
        assertEq(pool.liquidity(), expectedLiquidity);
    }
}
