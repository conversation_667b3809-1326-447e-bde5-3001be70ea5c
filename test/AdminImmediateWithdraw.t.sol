// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "./helpers/TestHelper.sol";

contract AdminImmediateWithdrawTest is TestHelper {
    function setUp() public override {
        super.setUp();
        
        // Set to BATCH_MODE for testing immediate withdrawals
        vm.prank(owner);
        pool.setOperationMode(RetailPool.OperationMode.BATCH_MODE);
    }

    function testAdminWithdrawImmediatelySuccess() public {
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT
        uint256 adminWithdrawAmount = 500 * 1e6; // 500 USDT
        
        // Alice deposits 1000 USDT to create immediate pool
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();
        
        // Check immediate pool has the funds
        assertEq(pool.immediatePool(), depositAmount);
        
        // Fund manager attempts immediate withdrawal
        uint256 fundManagerUSDTBefore = usdt.balanceOf(fundManager);
        
        vm.startPrank(fundManager);
        bool success = pool.adminWithdrawImmediately(adminWithdrawAmount);
        vm.stopPrank();
        
        // Verify success
        assertTrue(success);
        
        // Verify fund manager received USDT
        assertEq(usdt.balanceOf(fundManager), fundManagerUSDTBefore + adminWithdrawAmount);
        
        // Verify immediate pool was reduced
        assertEq(pool.immediatePool(), depositAmount - adminWithdrawAmount);
    }

    function testAdminWithdrawImmediatelyInsufficientFunds() public {
        uint256 depositAmount = 500 * 1e6; // 500 USDT
        uint256 adminWithdrawAmount = 1000 * 1e6; // 1000 USDT (more than available)
        
        // Alice deposits 500 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();
        
        // Fund manager attempts immediate withdrawal (should fail)
        uint256 fundManagerUSDTBefore = usdt.balanceOf(fundManager);
        
        vm.startPrank(fundManager);
        bool success = pool.adminWithdrawImmediately(adminWithdrawAmount);
        vm.stopPrank();
        
        // Verify failure
        assertFalse(success);
        
        // Verify fund manager's USDT balance unchanged
        assertEq(usdt.balanceOf(fundManager), fundManagerUSDTBefore);
        
        // Verify immediate pool unchanged
        assertEq(pool.immediatePool(), depositAmount);
    }

    function testAdminWithdrawImmediatelyOnlyInBatchMode() public {
        // Switch to INSTANT_MODE
        vm.prank(owner);
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);
        
        uint256 adminWithdrawAmount = 500 * 1e6; // 500 USDT
        
        // Fund manager attempts immediate withdrawal (should fail in INSTANT_MODE)
        vm.startPrank(fundManager);
        bool success = pool.adminWithdrawImmediately(adminWithdrawAmount);
        vm.stopPrank();
        
        // Verify failure
        assertFalse(success);
    }

    function testCanAdminWithdrawImmediately() public {
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT
        
        // Alice deposits 1000 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();
        
        // Check query function
        assertTrue(pool.canAdminWithdrawImmediately(500 * 1e6));  // 500 USDT - should be possible
        assertTrue(pool.canAdminWithdrawImmediately(1000 * 1e6)); // 1000 USDT - should be possible
        assertFalse(pool.canAdminWithdrawImmediately(1500 * 1e6)); // 1500 USDT - should not be possible
        
        // In INSTANT_MODE, should always return false
        // First trigger epoch to clear immediate pool, then switch mode
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();
        
        vm.prank(owner);
        pool.setOperationMode(RetailPool.OperationMode.INSTANT_MODE);
        
        assertFalse(pool.canAdminWithdrawImmediately(500 * 1e6));
    }

    function testAdminImmediateWithdrawEvent() public {
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT
        uint256 adminWithdrawAmount = 500 * 1e6; // 500 USDT
        
        // Alice deposits 1000 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();
        
        // Expect event emission
        vm.expectEmit(true, false, false, true);
        emit AdminImmediateWithdrawCompleted(fundManager, adminWithdrawAmount);
        
        // Fund manager performs immediate withdrawal
        vm.startPrank(fundManager);
        pool.adminWithdrawImmediately(adminWithdrawAmount);
        vm.stopPrank();
    }

    function testOnlyFundManagerCanAdminWithdrawImmediately() public {
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT
        uint256 adminWithdrawAmount = 500 * 1e6; // 500 USDT
        
        // Alice deposits 1000 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();
        
        // Alice (non-fund-manager) tries to do admin immediate withdrawal
        vm.startPrank(alice);
        vm.expectRevert();
        pool.adminWithdrawImmediately(adminWithdrawAmount);
        vm.stopPrank();
        
        // Bob (non-fund-manager) tries to do admin immediate withdrawal
        vm.startPrank(bob);
        vm.expectRevert();
        pool.adminWithdrawImmediately(adminWithdrawAmount);
        vm.stopPrank();
        
        // Fund manager should succeed
        vm.startPrank(fundManager);
        bool success = pool.adminWithdrawImmediately(adminWithdrawAmount);
        assertTrue(success);
        vm.stopPrank();
    }

    function testAdminImmediateWithdrawWithUserWithdraw() public {
        uint256 depositAmount = 2000 * 1e6; // 2000 USDT
        uint256 userWithdrawRPT = 500 * 1e18; // 500 RPT
        uint256 adminWithdrawAmount = 800 * 1e6; // 800 USDT
        
        // Alice deposits 2000 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();
        
        // Bob gets some RPT and withdraws immediately
        vm.startPrank(bob);
        usdt.approve(address(pool), 500 * 1e6);
        pool.deposit(500 * 1e6);
        
        bool userSuccess = pool.withdrawImmediately(userWithdrawRPT);
        assertTrue(userSuccess);
        vm.stopPrank();
        
        // Now immediate pool should be: 2000 + 500 - 500 = 2000
        assertEq(pool.immediatePool(), 2000 * 1e6);
        
        // Fund manager withdraws 800 USDT immediately
        vm.startPrank(fundManager);
        bool adminSuccess = pool.adminWithdrawImmediately(adminWithdrawAmount);
        assertTrue(adminSuccess);
        vm.stopPrank();
        
        // Final immediate pool should be: 2000 - 800 = 1200
        assertEq(pool.immediatePool(), 1200 * 1e6);
    }

    function testAdminWithdrawImmediatelyZeroAmount() public {
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT
        
        // Alice deposits 1000 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();
        
        // Fund manager tries to withdraw 0 USDT (should revert)
        vm.startPrank(fundManager);
        vm.expectRevert(abi.encodeWithSignature("InvalidAmount()"));
        pool.adminWithdrawImmediately(0);
        vm.stopPrank();
    }

    function testAdminImmediateWithdrawRespectsLiquidityLimits() public {
        // This test ensures admin immediate withdraw respects the same liquidity limits
        // as regular admin withdrawals
        
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT
        
        // Alice deposits 1000 USDT
        vm.startPrank(alice);
        usdt.approve(address(pool), depositAmount);
        pool.deposit(depositAmount);
        vm.stopPrank();
        
        // Create some user withdrawal requests to reduce available admin liquidity
        vm.startPrank(bob);
        usdt.approve(address(pool), 500 * 1e6);
        pool.deposit(500 * 1e6);
        
        // Bob requests traditional withdrawal (not immediate)
        uint256 requestId = pool.requestWithdraw(500 * 1e18);
        vm.stopPrank();
        
        // Now admin liquidity should be reduced
        // Fund manager tries to withdraw more than available admin liquidity
        vm.startPrank(fundManager);
        bool success = pool.adminWithdrawImmediately(1400 * 1e6); // More than available
        assertFalse(success); // Should fail due to liquidity limits
        vm.stopPrank();
    }

    // Event declaration for testing
    event AdminImmediateWithdrawCompleted(address indexed admin, uint256 usdt);
}
