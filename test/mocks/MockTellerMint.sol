// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "../../src/strategy/ITellerMint.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title MockTellerMint
 * @notice Mock implementation of ITellerMint for testing deposit functionality
 */
contract MockTellerMint is ITellerMint {
    IERC20 public asset;
    uint256 public totalShares;
    uint256 public totalAssets;

    mapping(address => uint256) public shareBalances;

    constructor(address _asset) {
        asset = IERC20(_asset);
    }

    function deposit(uint256 assets, address receiver) external override returns (uint256 shares) {
        require(asset.transferFrom(msg.sender, address(this), assets), "Transfer failed");

        // Simple 1:1 ratio for mock, but can be customized
        shares = _calculateShares(assets);
        shareBalances[receiver] += shares;
        totalShares += shares;
        totalAssets += assets;

        return shares;
    }

    function previewDeposit(uint256 assets) external view override returns (uint256 shares) {
        return _calculateShares(assets);
    }

    // Helper functions for testing
    function setShareBalance(address user, uint256 shares) external {
        shareBalances[user] = shares;
    }

    function setTotalShares(uint256 _totalShares) external {
        totalShares = _totalShares;
    }

    function setTotalAssets(uint256 _totalAssets) external {
        totalAssets = _totalAssets;
    }

    function getShareBalance(address user) external view returns (uint256) {
        return shareBalances[user];
    }

    // Simulate strategy gains/losses
    function simulateGain(uint256 amount) external {
        totalAssets += amount;
    }

    function simulateLoss(uint256 amount) external {
        if (amount > totalAssets) {
            totalAssets = 0;
        } else {
            totalAssets -= amount;
        }
    }

    // Internal function to calculate shares based on current ratio
    function _calculateShares(uint256 assets) internal view returns (uint256) {
        if (totalShares == 0 || totalAssets == 0) {
            return assets; // 1:1 ratio when pool is empty
        }
        return (assets * totalShares) / totalAssets;
    }
}
