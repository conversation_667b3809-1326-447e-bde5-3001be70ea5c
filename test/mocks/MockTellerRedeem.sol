// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "../../src/strategy/ITellerRedeem.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title MockTellerRedeem
 * @notice Mock implementation of ITellerRedeem for testing
 */
contract MockTellerRedeem is ITellerRedeem {
    IERC20 public asset;
    uint256 public totalShares;
    uint256 public totalAssets;
    uint256 private nextRequestIdCounter = 1;
    uint256 public constant COOLDOWN_PERIOD = 3 days;

    // Track requests for simulation
    struct MockRequest {
        address owner;
        uint256 shares;
        uint256 assets;
        uint256 timestamp;
        bool claimed;
    }

    mapping(bytes32 => MockRequest) public requests;

    constructor(address _asset) {
        asset = IERC20(_asset);
    }

    /**
     * @notice Set total assets for testing price calculations
     */
    function setTotalAssets(uint256 _totalAssets) external {
        totalAssets = _totalAssets;
    }

    /**
     * @notice Set total shares for testing
     */
    function setTotalShares(uint256 _totalShares) external {
        totalShares = _totalShares;
    }

    /**
     * @notice Add shares when deposits are made (called by tests)
     */
    function addShares(uint256 shares) external {
        totalShares += shares;
    }

    function redeem(uint256 shares, address receiver, address owner) external override returns (bytes32 requestId) {
        require(shares > 0, "MockTellerRedeem: Zero shares");
        require(shares <= totalShares, "MockTellerRedeem: Insufficient shares");

        // Calculate assets equivalent
        uint256 assets = totalAssets > 0 ? (shares * totalAssets) / totalShares : shares;

        // Generate bytes32 requestId from counter
        requestId = bytes32(nextRequestIdCounter++);
        requests[requestId] =
            MockRequest({owner: owner, shares: shares, assets: assets, timestamp: block.timestamp, claimed: false});

        // Reduce total shares
        totalShares -= shares;

        return requestId;
    }

    function claim(bytes32 requestId) external override returns (uint256 assets) {
        MockRequest storage request = requests[requestId];
        require(request.owner != address(0), "MockTellerRedeem: Invalid request");
        require(!request.claimed, "MockTellerRedeem: Already claimed");
        require(block.timestamp >= request.timestamp + COOLDOWN_PERIOD, "MockTellerRedeem: Cooldown not finished");

        assets = request.assets;
        request.claimed = true;

        // Transfer assets to the caller
        require(asset.transfer(msg.sender, assets), "MockTellerRedeem: Transfer failed");

        return assets;
    }

    function previewRedeem(uint256 shares) external view override returns (uint256 assets) {
        if (totalShares == 0) return shares; // 1:1 ratio when no shares exist
        return (shares * totalAssets) / totalShares;
    }

    function cooldownPeriod() external pure override returns (uint256) {
        return COOLDOWN_PERIOD;
    }

    /**
     * @notice Mint assets to this contract for testing redemptions
     */
    function mintAssets(uint256 amount) external {
        totalAssets += amount;
        require(asset.transferFrom(msg.sender, address(this), amount), "MockTellerRedeem: Transfer failed");
    }
}
