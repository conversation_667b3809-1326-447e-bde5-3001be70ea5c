// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import {Test} from "forge-std/Test.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

import "../../src/RetailPool.sol";
import "../../src/token/RPT.sol";
import "../../src/token/StakedRPT.sol";
import "../../src/strategy/ITellerMint.sol";
import "../../src/strategy/ITellerRedeem.sol";
import "../../src/mocks/MockUSDT.sol";
import "../mocks/MockTellerMint.sol";
import "../mocks/MockTellerRedeem.sol";

/**
 * @title TestHelper
 * @notice Base test helper contract with common setup
 */
contract TestHelper is Test {
    // Constants
    uint256 constant USDT_DECIMALS = 6;
    uint256 constant RPT_DECIMALS = 18;

    // Test accounts
    address owner = makeAddr("owner");
    address fundManager = makeAddr("fundManager");
    address alice = makeAddr("alice");
    address bob = makeAddr("bob");
    address carol = makeAddr("carol");

    // Core contracts
    MockUSDT usdt;
    MockTellerMint mockMintStrategy;
    MockTellerRedeem mockRedeemStrategy;

    // Proxy contracts
    ERC1967Proxy rptProxy;
    ERC1967Proxy stRPTProxy;
    ERC1967Proxy poolProxy;

    // Interface contracts
    RPT rpt;
    StakedRPT stRPT;
    RetailPool pool;
    ITellerMint mintStrategy;
    ITellerRedeem redeemStrategy;

    function setUp() public virtual {
        // Deploy mock tokens first
        usdt = new MockUSDT();
        mockMintStrategy = new MockTellerMint(address(usdt));
        mockRedeemStrategy = new MockTellerRedeem(address(usdt));

        // Deploy implementation contracts
        RPT rptImpl = new RPT();
        StakedRPT stRPTImpl = new StakedRPT();
        RetailPool poolImpl = new RetailPool();
        // Strategy implementation removed - using interface only

        // Deploy proxies with initialization
        rptProxy = new ERC1967Proxy(
            address(rptImpl), abi.encodeWithSelector(RPT.initialize.selector, "Retail Pool Token", "RPT", owner)
        );

        // Use mock strategies directly for tests
        mintStrategy = ITellerMint(address(mockMintStrategy));
        redeemStrategy = ITellerRedeem(address(mockRedeemStrategy));

        // Deploy proxies in correct order
        poolProxy = new ERC1967Proxy(
            address(poolImpl),
            abi.encodeWithSelector(
                RetailPool.initialize.selector,
                address(usdt),
                address(rptProxy),
                address(0), // stRPT will be set later
                address(mockMintStrategy),
                address(mockRedeemStrategy),
                owner,
                fundManager
            )
        );

        stRPTProxy = new ERC1967Proxy(
            address(stRPTImpl),
            abi.encodeWithSelector(
                StakedRPT.initialize.selector,
                "Staked Retail Pool Token",
                "stRPT",
                owner,
                address(poolProxy),
                address(rptProxy)
            )
        );

        // Cast proxies to interfaces
        rpt = RPT(address(rptProxy));
        stRPT = StakedRPT(address(stRPTProxy));
        pool = RetailPool(address(poolProxy));

        // Grant necessary roles (as owner)
        vm.startPrank(owner);

        // Grant pool minter/burner roles for RPT
        rpt.grantRole(rpt.MINTER_ROLE(), address(pool));
        rpt.grantRole(rpt.BURNER_ROLE(), address(pool));

        // Grant pool minter/burner roles for stRPT
        stRPT.grantRole(stRPT.MINTER_ROLE(), address(pool));
        // ERC20 version only needs MINTER_ROLE for both mint and burn

        // RPT reference is set during StakedRPT initialization

        vm.stopPrank();

        // Mint initial USDT to test accounts
        usdt.mint(alice, 1_000_000 * 10 ** USDT_DECIMALS);
        usdt.mint(bob, 1_000_000 * 10 ** USDT_DECIMALS);
        usdt.mint(carol, 1_000_000 * 10 ** USDT_DECIMALS);
        usdt.mint(fundManager, 1_000_000 * 10 ** USDT_DECIMALS);

        // Approve pool to spend USDT
        vm.prank(alice);
        usdt.approve(address(pool), type(uint256).max);
        vm.prank(bob);
        usdt.approve(address(pool), type(uint256).max);
        vm.prank(carol);
        usdt.approve(address(pool), type(uint256).max);
        vm.prank(fundManager);
        usdt.approve(address(pool), type(uint256).max);
    }

    /**
     * @notice Helper function to sync shares between mint and redeem strategies
     * @dev Call this after deposits to ensure MockTellerRedeem knows about the shares
     */
    function syncStrategyShares() internal {
        uint256 totalShares = mockMintStrategy.totalShares();
        uint256 totalAssets = mockMintStrategy.totalAssets();
        mockRedeemStrategy.setTotalShares(totalShares);
        mockRedeemStrategy.setTotalAssets(totalAssets);
    }
}
