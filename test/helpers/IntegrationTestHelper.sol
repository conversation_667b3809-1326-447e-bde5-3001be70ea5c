// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

import "../../src/RetailPool.sol";
import "../../src/token/RPT.sol";
import "../../src/token/StakedRPT.sol";
import "../../src/strategy/ITellerMint.sol";
import "../../src/strategy/ITellerRedeem.sol";
import "../../src/mocks/MockUSDT.sol";

/**
 * @title Integration Test Helper
 * @notice Base test setup for integration tests using real Teller contracts
 */
abstract contract IntegrationTestHelper is Test {
    // Test accounts
    address owner = makeAddr("owner");
    address fundManager = makeAddr("fundManager");
    address alice = makeAddr("alice");
    address bob = makeAddr("bob");
    address carol = makeAddr("carol");

    // Core contracts
    MockUSDT usdt;

    // Proxy contracts
    ERC1967Proxy rptProxy;
    ERC1967Proxy stRPTProxy;
    ERC1967Proxy poolProxy;

    // Interface contracts
    RPT rpt;
    StakedRPT stRPT;
    RetailPool pool;
    ITellerMint mintStrategy;
    ITellerRedeem redeemStrategy;

    // Real strategy addresses from environment
    address tellerMinterAddr;
    address tellerRedeemAddr;
    address usdtAddr;

    // Constants
    uint8 public constant USDT_DECIMALS = 6;

    function setUp() public virtual {
        // Load real contract addresses from environment
        tellerMinterAddr = vm.envOr("TellerMinterAddr", address(0));
        tellerRedeemAddr = vm.envOr("TellerRedeemAddr", address(0));
        usdtAddr = vm.envOr("UsdtAddr", address(0));

        // Skip test if environment variables not set
        if (tellerMinterAddr == address(0) || tellerRedeemAddr == address(0)) {
            console.log("Skipping integration tests: TellerMinterAddr or TellerRedeemAddr not set");
            return;
        }

        // Use real USDT if provided, otherwise deploy mock
        if (usdtAddr != address(0)) {
            usdt = MockUSDT(usdtAddr);
        } else {
            usdt = new MockUSDT();
            console.log("Using Mock USDT for integration tests");
        }

        // Use real strategy contracts
        mintStrategy = ITellerMint(tellerMinterAddr);
        redeemStrategy = ITellerRedeem(tellerRedeemAddr);

        console.log("Using real Teller contracts:");
        console.log("Mint Strategy:", tellerMinterAddr);
        console.log("Redeem Strategy:", tellerRedeemAddr);

        // Deploy implementation contracts
        RPT rptImpl = new RPT();
        StakedRPT stRPTImpl = new StakedRPT();
        RetailPool poolImpl = new RetailPool();

        // Deploy proxies with initialization
        rptProxy = new ERC1967Proxy(
            address(rptImpl), abi.encodeWithSelector(RPT.initialize.selector, "Retail Pool Token", "RPT", owner)
        );

        // Deploy RetailPool proxy first with zero addresses for stRPT
        poolProxy = new ERC1967Proxy(
            address(poolImpl),
            abi.encodeWithSelector(
                RetailPool.initialize.selector,
                address(usdt),
                address(rptProxy),
                address(0), // stRPT will be set later
                tellerMinterAddr,
                tellerRedeemAddr,
                owner,
                fundManager
            )
        );

        stRPTProxy = new ERC1967Proxy(
            address(stRPTImpl),
            abi.encodeWithSelector(
                StakedRPT.initialize.selector,
                "Staked Retail Pool Token",
                "stRPT",
                owner,
                address(poolProxy),
                address(rptProxy)
            )
        );

        // Cast proxies to interfaces
        rpt = RPT(address(rptProxy));
        stRPT = StakedRPT(address(stRPTProxy));
        pool = RetailPool(address(poolProxy));

        // Grant necessary roles (as owner)
        vm.startPrank(owner);

        // Grant pool minter/burner roles for RPT
        rpt.grantRole(rpt.MINTER_ROLE(), address(pool));
        rpt.grantRole(rpt.BURNER_ROLE(), address(pool));

        // Grant pool minter role for stRPT
        stRPT.grantRole(stRPT.MINTER_ROLE(), address(pool));

        vm.stopPrank();

        // Mint initial USDT to test accounts if using mock USDT
        if (usdtAddr == address(0)) {
            usdt.mint(alice, 1_000_000 * 10 ** USDT_DECIMALS);
            usdt.mint(bob, 1_000_000 * 10 ** USDT_DECIMALS);
            usdt.mint(carol, 1_000_000 * 10 ** USDT_DECIMALS);
            usdt.mint(fundManager, 1_000_000 * 10 ** USDT_DECIMALS);

            // Approve pool to spend USDT
            vm.prank(alice);
            usdt.approve(address(pool), type(uint256).max);
            vm.prank(bob);
            usdt.approve(address(pool), type(uint256).max);
            vm.prank(carol);
            usdt.approve(address(pool), type(uint256).max);
            vm.prank(fundManager);
            usdt.approve(address(pool), type(uint256).max);
        }

        // Set initial epoch time to current time
        vm.warp(1000);
    }

    /**
     * @notice Check if integration test environment is properly set up
     */
    modifier requireIntegrationSetup() {
        if (tellerMinterAddr == address(0) || tellerRedeemAddr == address(0)) {
            console.log("Skipping test: Integration environment not configured");
            return;
        }
        _;
    }

    /**
     * @notice Helper function to check strategy balances
     */
    function checkStrategyBalances() internal view returns (uint256 mintAssets, uint256 redeemShares) {
        // Try to get strategy state - these calls might fail on real contracts
        try usdt.balanceOf(tellerMinterAddr) returns (uint256 assets) {
            mintAssets = assets;
        } catch {
            console.log("Could not read mint strategy assets");
        }

        if (pool.strategyShares() > 0) {
            try redeemStrategy.previewRedeem(pool.strategyShares()) returns (uint256 assets) {
                redeemShares = assets;
            } catch {
                console.log("Could not preview redeem from strategy");
            }
        }
    }

    /**
     * @notice Helper to fund test accounts with real USDT if needed
     */
    function fundTestAccounts() internal {
        if (usdtAddr != address(0)) {
            console.log("Warning: Using real USDT - ensure test accounts have sufficient balance");
            console.log("Test accounts need USDT at:");
            console.log("Alice:", alice);
            console.log("Bob:", bob);
            console.log("Carol:", carol);
            console.log("Fund Manager:", fundManager);
        }
    }
}
