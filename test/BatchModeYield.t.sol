// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

import "./helpers/TestHelper.sol";

/**
 * @title Batch Mode Yield Test Suite
 * @notice Tests for BATCH_MODE functionality with yield simulation
 */
contract BatchModeYieldTest is TestHelper {

    function setUp() public override {
        super.setUp();
        // Ensure we're in BATCH_MODE (default)
        assertEq(uint256(pool.operationMode()), uint256(RetailPool.OperationMode.BATCH_MODE));

        // Set initial epoch time
        vm.warp(1000);
    }

    // ================== Basic Batch Mode Tests ==================

    function testBatchModeDepositsWaitForEpoch() public {
        uint256 depositAmount = 1000 * 1e6; // 1000 USDT

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // In batch mode, funds should wait for epoch
        assertEq(pool.totalWaitDeposits(), depositAmount);
        assertEq(pool.strategyShares(), 0);
        assertEq(usdt.balanceOf(address(pool)), depositAmount);

        // RPT should be minted immediately
        assertEq(rpt.balanceOf(alice), 1000 * 1e18); // 1:1 ratio initially
    }

    function testBatchModeMultipleDepositsBeforeEpoch() public {
        uint256 aliceDeposit = 1000 * 1e6;
        uint256 bobDeposit = 500 * 1e6;
        uint256 carolDeposit = 2000 * 1e6;

        // Multiple deposits
        vm.prank(alice);
        pool.deposit(aliceDeposit);

        vm.prank(bob);
        pool.deposit(bobDeposit);

        vm.prank(carol);
        pool.deposit(carolDeposit);

        // All should accumulate in totalWaitDeposits
        uint256 totalDeposits = aliceDeposit + bobDeposit + carolDeposit;
        assertEq(pool.totalWaitDeposits(), totalDeposits);
        assertEq(pool.strategyShares(), 0);
        assertEq(usdt.balanceOf(address(pool)), totalDeposits);

        // RPT minted for each user
        assertEq(rpt.balanceOf(alice), 1000 * 1e18);
        assertEq(rpt.balanceOf(bob), 500 * 1e18);
        assertEq(rpt.balanceOf(carol), 2000 * 1e18);
    }

    function testBatchModeEpochProcessing() public {
        uint256 depositAmount = 1000 * 1e6;

        // Alice deposits
        vm.prank(alice);
        pool.deposit(depositAmount);

        // Fast forward to allow epoch trigger
        vm.warp(block.timestamp + 25 hours);

        // Trigger epoch
        pool.triggerEpoch();

        // Should have processed deposits
        assertEq(pool.totalWaitDeposits(), 0);
        assertEq(pool.strategyShares(), depositAmount);
        assertEq(usdt.balanceOf(address(pool)), 0);
    }

    // ================== Yield Simulation Tests ==================

    function testYieldAccrualIncreasesRPTPrice() public {
        uint256 initialDeposit = 1000 * 1e6; // 1000 USDT

        // Alice makes initial deposit
        vm.prank(alice);
        pool.deposit(initialDeposit);

        // Process through epoch
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Check initial state
        assertEq(pool.strategyShares(), initialDeposit);
        uint256 initialPrice = pool.rptPrice();
        assertEq(initialPrice, 1e18); // Should be 1:1 initially

        // Simulate yield in the strategy (10% gain)
        uint256 yieldAmount = 100 * 1e6; // 100 USDT yield
        mockMintStrategy.simulateGain(yieldAmount);

        // Also need to add assets to redeem strategy for consistent pricing
        mockRedeemStrategy.setTotalAssets(initialDeposit + yieldAmount);
        mockRedeemStrategy.setTotalShares(initialDeposit);

        // RPT price should increase
        uint256 newPrice = pool.rptPrice();
        assertGt(newPrice, initialPrice);

        // Price should be approximately 1.1 (10% increase)
        uint256 expectedPrice = 1.1e18;
        assertApproxEqRel(newPrice, expectedPrice, 0.01e18); // 1% tolerance
    }

    function testNewDepositAfterYieldGetsFewerRPT() public {
        uint256 initialDeposit = 1000 * 1e6; // 1000 USDT

        // Alice makes initial deposit
        vm.prank(alice);
        pool.deposit(initialDeposit);

        // Process through epoch
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Simulate yield (20% gain)
        uint256 yieldAmount = 200 * 1e6; // 200 USDT yield
        mockMintStrategy.simulateGain(yieldAmount);
        mockRedeemStrategy.setTotalAssets(initialDeposit + yieldAmount);
        mockRedeemStrategy.setTotalShares(initialDeposit);

        // Bob makes same deposit amount after yield
        uint256 bobDeposit = 1000 * 1e6;
        vm.prank(bob);
        pool.deposit(bobDeposit);

        // Bob should get fewer RPT due to higher price
        uint256 aliceRPT = rpt.balanceOf(alice);
        uint256 bobRPT = rpt.balanceOf(bob);

        assertEq(aliceRPT, 1000 * 1e18); // Alice got 1:1 ratio
        assertLt(bobRPT, 1000 * 1e18); // Bob gets less due to higher price

        // Bob should get approximately 833.33 RPT (1000 / 1.2)
        // We'll just check that Bob gets significantly less than Alice
        assertLt(bobRPT, aliceRPT * 9 / 10); // Bob should get less than 90% of Alice's amount
    }

    function testMultipleYieldCycles() public {
        uint256 depositAmount = 1000 * 1e6;

        // Initial deposit and epoch
        vm.prank(alice);
        pool.deposit(depositAmount);
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        uint256 price1 = pool.rptPrice();
        assertEq(price1, 1e18);

        // First yield cycle (10% gain)
        mockMintStrategy.simulateGain(100 * 1e6);
        mockRedeemStrategy.setTotalAssets(1100 * 1e6);
        mockRedeemStrategy.setTotalShares(1000 * 1e6);

        uint256 price2 = pool.rptPrice();
        assertApproxEqRel(price2, 1.1e18, 0.01e18);

        // Second yield cycle (another 10% gain on new total)
        mockMintStrategy.simulateGain(110 * 1e6); // 10% of 1100
        mockRedeemStrategy.setTotalAssets(1210 * 1e6);
        mockRedeemStrategy.setTotalShares(1000 * 1e6);

        uint256 price3 = pool.rptPrice();
        assertApproxEqRel(price3, 1.21e18, 0.01e18); // Compound growth

        // Third yield cycle (another 10% gain)
        mockMintStrategy.simulateGain(121 * 1e6); // 10% of 1210
        mockRedeemStrategy.setTotalAssets(1331 * 1e6);
        mockRedeemStrategy.setTotalShares(1000 * 1e6);

        uint256 price4 = pool.rptPrice();
        assertApproxEqRel(price4, 1.331e18, 0.01e18); // Compound growth
    }

    function testYieldWithMultipleUsersAndEpochs() public {
        // Alice deposits in first epoch
        vm.prank(alice);
        pool.deposit(1000 * 1e6);

        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Simulate yield
        mockMintStrategy.simulateGain(100 * 1e6); // 10% gain
        mockRedeemStrategy.setTotalAssets(1100 * 1e6);
        mockRedeemStrategy.setTotalShares(1000 * 1e6);

        // Bob deposits after yield
        vm.prank(bob);
        pool.deposit(1000 * 1e6);

        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Check that both users have appropriate RPT amounts
        uint256 aliceRPT = rpt.balanceOf(alice);
        uint256 bobRPT = rpt.balanceOf(bob);

        assertEq(aliceRPT, 1000 * 1e18); // Alice got original 1:1 ratio
        assertLt(bobRPT, 1000 * 1e18); // Bob got fewer due to higher price

        // Total RPT should be less than 2000 due to price appreciation
        uint256 totalRPT = aliceRPT + bobRPT;
        assertLt(totalRPT, 2000 * 1e18);
    }

    function testWithdrawAfterYieldReflectsGains() public {
        uint256 depositAmount = 1000 * 1e6;

        // Alice deposits and processes through epoch
        vm.prank(alice);
        pool.deposit(depositAmount);
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Simulate significant yield (50% gain)
        uint256 yieldAmount = 500 * 1e6;
        mockMintStrategy.simulateGain(yieldAmount);
        mockRedeemStrategy.setTotalAssets(depositAmount + yieldAmount);
        mockRedeemStrategy.setTotalShares(depositAmount);

        // Alice withdraws half her RPT
        uint256 withdrawRPT = 500 * 1e18;
        vm.prank(alice);
        uint256 requestId = pool.requestWithdraw(withdrawRPT);

        // Check the USDT amount she should receive
        (,, uint256 usdtAmount,,,,) = pool.withdrawRequests(requestId);

        // She should get more than 500 USDT due to yield
        assertGt(usdtAmount, 500 * 1e6);

        // Should be approximately 750 USDT (500 RPT * 1.5 price)
        uint256 expectedUSDT = (500 * 1e6 * 15) / 10; // 500 * 1.5
        assertApproxEqRel(usdtAmount, expectedUSDT, 0.01e18);
    }

    // ================== Fund Injection with Yield Tests ==================

    function testFundInjectionWithYield() public {
        uint256 userDeposit = 1000 * 1e6;
        uint256 injectionAmount = 500 * 1e6;

        // User deposit
        vm.prank(alice);
        pool.deposit(userDeposit);

        // Fund manager injection
        vm.prank(fundManager);
        usdt.approve(address(pool), injectionAmount);
        vm.prank(fundManager);
        pool.injectFunds(injectionAmount);

        // Process epoch
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Total should be staked
        assertEq(pool.strategyShares(), userDeposit + injectionAmount);

        // Simulate yield on total amount
        uint256 totalAmount = userDeposit + injectionAmount;
        uint256 yieldAmount = totalAmount / 10; // 10% yield
        mockMintStrategy.simulateGain(yieldAmount);
        mockRedeemStrategy.setTotalAssets(totalAmount + yieldAmount);
        mockRedeemStrategy.setTotalShares(totalAmount);

        // RPT price should increase
        uint256 newPrice = pool.rptPrice();
        assertGt(newPrice, 1e18);

        // User should benefit from the injected funds' yield too
        // Price should be (1500 + 150) / 1000 = 1.65 (since only user got RPT, not fund manager)
        assertApproxEqRel(newPrice, 1.65e18, 0.01e18);
    }

    // ================== Advanced Yield Scenarios ==================

    function testYieldLossScenario() public {
        uint256 depositAmount = 1000 * 1e6;

        // Alice deposits and processes through epoch
        vm.prank(alice);
        pool.deposit(depositAmount);
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Simulate loss (20% loss)
        uint256 lossAmount = 200 * 1e6;
        mockMintStrategy.simulateLoss(lossAmount);
        mockRedeemStrategy.setTotalAssets(depositAmount - lossAmount);
        mockRedeemStrategy.setTotalShares(depositAmount);

        // RPT price should decrease
        uint256 newPrice = pool.rptPrice();
        assertLt(newPrice, 1e18);

        // Price should be approximately 0.8 (20% loss)
        assertApproxEqRel(newPrice, 0.8e18, 0.01e18);
    }

    function testComplexYieldScenarioWithMultipleEpochs() public {
        // Epoch 1: Alice deposits
        vm.prank(alice);
        pool.deposit(1000 * 1e6);
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Simulate 10% yield
        mockMintStrategy.simulateGain(100 * 1e6);
        mockRedeemStrategy.setTotalAssets(1100 * 1e6);
        mockRedeemStrategy.setTotalShares(1000 * 1e6);

        // Epoch 2: Bob deposits at higher price
        vm.prank(bob);
        pool.deposit(1100 * 1e6); // Same USDT value as Alice's appreciated position
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Simulate another 10% yield on total
        mockMintStrategy.simulateGain(220 * 1e6); // 10% of 2200
        mockRedeemStrategy.setTotalAssets(2420 * 1e6);
        mockRedeemStrategy.setTotalShares(2000 * 1e6); // Alice's 1000 + Bob's 1000

        // Check final RPT balances
        uint256 aliceRPT = rpt.balanceOf(alice);
        uint256 bobRPT = rpt.balanceOf(bob);

        assertEq(aliceRPT, 1000 * 1e18); // Alice got original 1:1
        assertEq(bobRPT, 1000 * 1e18); // Bob got 1100 USDT / 1.1 price = 1000 RPT

        // Both should have equal RPT but Alice invested less USDT
        assertEq(aliceRPT, bobRPT);

        // Final price should be 2420 / 2000 = 1.21
        uint256 finalPrice = pool.rptPrice();
        assertApproxEqRel(finalPrice, 1.21e18, 0.01e18);
    }

    function testWithdrawalsInBatchModeWithYield() public {
        uint256 depositAmount = 1000 * 1e6;

        // Alice deposits and processes through epoch
        vm.prank(alice);
        pool.deposit(depositAmount);
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Simulate yield
        mockMintStrategy.simulateGain(200 * 1e6); // 20% gain
        mockRedeemStrategy.setTotalAssets(1200 * 1e6);
        mockRedeemStrategy.setTotalShares(1000 * 1e6);

        // Alice requests withdrawal of half her RPT
        vm.prank(alice);
        uint256 requestId = pool.requestWithdraw(500 * 1e18);

        // In batch mode, this should be tracked in userWaitWithdrawals
        assertEq(pool.userWaitWithdrawals(), 600 * 1e6); // 500 RPT * 1.2 price

        // Process withdrawal through epoch
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // Should have initiated strategy redeem
        assertEq(pool.userWaitWithdrawals(), 0); // Reset after epoch

        // In batch mode, we need to ensure the pool has enough USDT for withdrawals
        // The epoch should have processed the strategy redeem and brought funds back
        // First check Alice's current balance
        uint256 aliceBalanceBefore = usdt.balanceOf(alice);

        // Mint exactly what's needed for the withdrawal
        usdt.mint(address(pool), 600 * 1e6);

        // Fast forward past withdrawal wait period
        vm.warp(block.timestamp + 8 days);

        // In batch mode, withdrawal requests don't have associated strategy requests
        // So we can claim directly after wait period
        vm.prank(alice);
        pool.claimWithdraw(requestId);

        // Alice should receive 600 USDT (500 RPT * 1.2 price)
        assertEq(usdt.balanceOf(alice), aliceBalanceBefore + 600 * 1e6);
    }

    function testMockStrategyConsistency() public {
        // Test that our mock strategies maintain consistent state
        uint256 depositAmount = 1000 * 1e6;

        // Initial deposit
        vm.prank(alice);
        pool.deposit(depositAmount);
        vm.warp(block.timestamp + 25 hours);
        pool.triggerEpoch();

        // After epoch, sync redeem strategy with mint strategy state
        mockRedeemStrategy.setTotalShares(mockMintStrategy.totalShares());
        mockRedeemStrategy.setTotalAssets(mockMintStrategy.totalAssets());

        // Check initial state
        assertEq(mockMintStrategy.totalShares(), depositAmount);
        assertEq(mockMintStrategy.totalAssets(), depositAmount);
        assertEq(mockRedeemStrategy.totalShares(), depositAmount);
        assertEq(mockRedeemStrategy.totalAssets(), depositAmount);

        // Simulate yield
        uint256 yieldAmount = 100 * 1e6;
        mockMintStrategy.simulateGain(yieldAmount);
        mockRedeemStrategy.setTotalAssets(depositAmount + yieldAmount);

        // Check consistency
        assertEq(mockMintStrategy.totalAssets(), depositAmount + yieldAmount);
        assertEq(mockRedeemStrategy.totalAssets(), depositAmount + yieldAmount);

        // Preview functions should return consistent values
        uint256 mintPreview = mockMintStrategy.previewDeposit(100 * 1e6);
        uint256 redeemPreview = mockRedeemStrategy.previewRedeem(100 * 1e6);

        // Both should reflect the yield (assets > shares ratio)
        assertLt(mintPreview, 100 * 1e6); // Less shares for same assets due to yield
        assertGt(redeemPreview, 100 * 1e6); // More assets for same shares due to yield
    }
}
